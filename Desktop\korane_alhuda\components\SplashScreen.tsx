import React, { useState, useEffect } from 'react';

const FULL_TEXT = "بسم الله الرحمن الرحيم";
const ANIMATION_DURATION = 5000; // 5 seconds in milliseconds
const POST_ANIMATION_DELAY = 2000; // 2 seconds delay after animation completes

interface SplashScreenProps {
  onFinished: () => void;
}

export const SplashScreen: React.FC<SplashScreenProps> = ({ onFinished }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < FULL_TEXT.length) {
      const charDelay = ANIMATION_DURATION / FULL_TEXT.length;
      const timer = setTimeout(() => {
        setDisplayText((prev) => prev + FULL_TEXT[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, charDelay);
      return () => clearTimeout(timer);
    }
  }, [currentIndex]);

  useEffect(() => {
    // Call onFinished after the total animation duration plus the post-animation delay
    // Add a small buffer to ensure text animation fully completes if needed
    const finishTimer = setTimeout(() => {
      onFinished();
    }, ANIMATION_DURATION + POST_ANIMATION_DELAY + 300); 
    return () => clearTimeout(finishTimer);
  }, [onFinished]);

  return (
    <div className="fixed inset-0 bg-primary flex items-center justify-center z-[100]" aria-hidden="true">
      <h1 className="text-white font-quran text-5xl sm:text-6xl md:text-7xl lg:text-8xl p-4 text-center" dir="rtl">
        {displayText}
      </h1>
    </div>
  );
};