import { ALADHAN_API_BASE_URL } from '../constants';
import { AlAdhanApiResponse, AlAdhanData } from '../types';

export const fetchPrayerTimesByCoordinates = async (
  latitude: number, 
  longitude: number, 
  method: number,
  school?: 'standard' | 'hanafi' // Optional school parameter
): Promise<AlAdhanData> => {
  let url = `${ALADHAN_API_BASE_URL}/timings?latitude=${latitude}&longitude=${longitude}&method=${method}`;
  if (school) {
    url += `&school=${school}`;
  }
  const response = await fetch(url);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to fetch prayer times: ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const data: AlAdhanApiResponse = await response.json();
  if (data.code !== 200 || data.status !== "OK") {
    throw new Error(`API Error: ${data.status}`);
  }
  return data.data;
};

export const fetchPrayerTimesByCity = async (
  city: string, 
  country: string, 
  method: number,
  school?: 'standard' | 'hanafi' // Optional school parameter
): Promise<AlAdhanData> => {
  const countryParam = country ? `&country=${encodeURIComponent(country)}` : '';
  let url = `${ALADHAN_API_BASE_URL}/timingsByCity?city=${encodeURIComponent(city)}${countryParam}&method=${method}`;
  if (school) {
    url += `&school=${school}`;
  }
  const response = await fetch(url);
   if (!response.ok) {
    const errorData = await response.json().catch(() => ({})); 
    throw new Error(`Failed to fetch prayer times for ${city}: ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const data: AlAdhanApiResponse = await response.json();
   if (data.code !== 200 || data.status !== "OK") {
    if (typeof data.data === 'string') {
        throw new Error(`API Error for ${city}: ${data.data}`);
    }
    throw new Error(`API Error for ${city}: ${data.status}`);
  }
  if (typeof data.data !== 'object' || data.data === null || !('timings' in data.data)) {
    throw new Error(`Invalid data structure received for ${city}. Please check city/country.`);
  }
  return data.data as AlAdhanData;
};
