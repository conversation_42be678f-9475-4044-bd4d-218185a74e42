
import React, { useState, useEffect, useCallback } from 'react';
import { Sidebar } from './components/Navbar'; // Renamed Navbar to Sidebar for clarity
import { PrayerTimesSection } from './components/PrayerTimesSection';
import { QuranSection } from './components/QuranSection';
import { TasbihSection } from './components/TasbihSection';
import { HadithSection } from './components/HadithSection';
import { AiAdhkarGeneratorSection } from './components/AiAdhkarGeneratorSection';
import { AiDuaGeneratorSection } from './components/AiDuaGeneratorSection'; 
import { AsmaulHusnaSection } from './components/AsmaulHusnaSection';
import { SettingsSection } from './components/SettingsSection'; 
import { AppSection, AppSettings, QuranSettings as QuranSettingsType } from './types'; 
import { DEFAULT_APP_SETTINGS } from './constants';
import { BookOpenIcon, Bars3Icon } from './components/icons'; 
import { SplashScreen } from './components/SplashScreen';

const App: React.FC = () => {
  const [showSplash, setShowSplash] = useState<boolean>(true);
  const [activeSection, setActiveSection] = useState<AppSection>(AppSection.PrayerTimes);
  const [appSettings, setAppSettings] = useState<AppSettings>(() => {
    if (typeof window !== 'undefined') {
      const storedSettings = localStorage.getItem('islamicAppHubSettings');
      if (storedSettings) {
        try {
          const parsed = JSON.parse(storedSettings);
          // Deep merge for nested settings objects
          return {
            general: { ...DEFAULT_APP_SETTINGS.general, ...(parsed.general || {}) },
            prayerTimes: { ...DEFAULT_APP_SETTINGS.prayerTimes, ...(parsed.prayerTimes || {}) },
            quran: { 
              ...DEFAULT_APP_SETTINGS.quran, 
              ...(parsed.quran || {}),
              mushafViewSettings: {
                ...DEFAULT_APP_SETTINGS.quran.mushafViewSettings,
                ...(parsed.quran?.mushafViewSettings || {})
              }
            }
          };
        } catch (e) {
          console.error("Failed to parse settings from localStorage", e);
        }
      }
    }
    return DEFAULT_APP_SETTINGS;
  });

  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [isAppHeaderVisible, setIsAppHeaderVisible] = useState<boolean>(true);


  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('islamicAppHubSettings', JSON.stringify(appSettings));
      
      document.documentElement.lang = appSettings.general.appLanguage;
      const direction = appSettings.general.appLanguage === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.dir = direction;
      document.body.dir = direction;

      if (appSettings.general.themeMode === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [appSettings]);

  const handleUpdateSettings = useCallback((newSettings: Partial<AppSettings>) => {
    setAppSettings(prevSettings => {
      const updatedSettings = {
        ...prevSettings,
        ...newSettings,
        general: { ...prevSettings.general, ...(newSettings.general || {}) },
        prayerTimes: { ...prevSettings.prayerTimes, ...(newSettings.prayerTimes || {}) },
        quran: { 
          ...prevSettings.quran, 
          ...(newSettings.quran || {}),
          mushafViewSettings: {
            ...prevSettings.quran.mushafViewSettings,
            ...(newSettings.quran?.mushafViewSettings || {})
          }
        }
      };
      return updatedSettings;
    });
  }, []);

  const handleSplashFinished = () => {
    setShowSplash(false);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  if (showSplash) {
    return <SplashScreen onFinished={handleSplashFinished} />;
  }

  const renderSection = () => {
    switch (activeSection) {
      case AppSection.PrayerTimes:
        return <PrayerTimesSection settings={appSettings.prayerTimes} />;
      case AppSection.Quran:
        return <QuranSection 
                  quranSettings={appSettings.quran} 
                  onUpdateQuranSettings={(newQuranSettings) => 
                    handleUpdateSettings({ 
                      quran: { 
                        ...appSettings.quran, 
                        ...newQuranSettings,
                        mushafViewSettings: {
                          ...appSettings.quran.mushafViewSettings,
                          ...(newQuranSettings.mushafViewSettings || {})
                        }
                      } 
                    })
                  }
                  onSetAppHeaderVisibility={setIsAppHeaderVisible} 
                />;
      case AppSection.Hadith:
        return <HadithSection />;
      case AppSection.AsmaulHusna:
        return <AsmaulHusnaSection />;
      case AppSection.Adhkar: 
        return <AiAdhkarGeneratorSection />;
      case AppSection.AiDuaGenerator:
        return <AiDuaGeneratorSection />;
      case AppSection.Tasbih:
        return <TasbihSection />;
      case AppSection.Settings:
        return <SettingsSection currentSettings={appSettings} onSettingsChange={handleUpdateSettings} />;
      default:
        return <PrayerTimesSection settings={appSettings.prayerTimes} />;
    }
  };

  return (
    <div className={`min-h-screen flex flex-col bg-background-default text-text-default transition-colors duration-300`}>
      {isAppHeaderVisible && (
        <header className="relative max-w-xl mx-auto my-6 py-6 px-4 bg-card-bg shadow-xl rounded-2xl text-center">
          <button
            onClick={toggleSidebar}
            className="absolute top-1/2 -translate-y-1/2 start-4 p-2 text-primary hover:text-primary-dark focus:outline-none focus:ring-2 focus:ring-primary-light rounded-md"
            aria-label="فتح القائمة"
          >
            <Bars3Icon className="w-7 h-7" />
          </button>
          <BookOpenIcon className="w-12 h-12 text-primary mx-auto mb-3" />
          <h1 className="text-3xl sm:text-4xl font-bold text-primary">
            قرآن الهدى
          </h1>
          <p className="text-md sm:text-lg text-text-dark mt-1">
            مرجعك الإسلامي الشامل لأوقات الصلاة، القرآن، الحديث، والأذكار.
          </p>
        </header>
      )}
      <main className={`flex-grow container mx-auto p-4 sm:p-6 lg:p-8 ${!isAppHeaderVisible && activeSection === AppSection.Quran ? 'p-0 sm:p-0 lg:p-0' : ''}`}>
        {renderSection()}
      </main>
      
      {/* Overlay for Sidebar */}
      {isSidebarOpen && isAppHeaderVisible && ( // Sidebar overlay only if header (and thus sidebar button) is visible
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
          onClick={closeSidebar}
          aria-hidden="true"
        ></div>
      )}

      {isAppHeaderVisible && ( // Sidebar itself only if header is visible
        <Sidebar 
          isOpen={isSidebarOpen}
          onClose={closeSidebar}
          activeSection={activeSection} 
          setActiveSection={(section) => {
            setActiveSection(section);
            closeSidebar(); // Close sidebar on section change
          }}
        />
      )}
    </div>
  );
};

export default App;
