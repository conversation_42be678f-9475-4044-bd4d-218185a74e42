// data/surahStartPages.ts
// Maps Surah number to its starting page number in a 604-page Mushaf (e.g., KSU Mushaf)
export const surahStartPages: Record<number, number> = {
  1: 1,    // Al-Fatihah
  2: 2,    // Al-Baqarah
  3: 50,   // Aal-i-Imran
  4: 77,   // An-<PERSON><PERSON>
  5: 106,  // Al-Ma'idah
  // ... (Entries for all 114 Surahs are needed for full functionality)
  // Example for a middle Surah
  18: 293, // Al-Kahf
  // Example for a late Surah
  36: 440, // Ya-Sin
  // Example for a short Surah near the end
  112: 604, // Al-<PERSON><PERSON>
  113: 604, // Al-Falaq
  114: 604, // An-Nas
};

// Helper function to get all surah numbers for dropdown
export const getAllSurahNumbersForMushaf = (): number[] => {
  return Array.from({ length: 114 }, (_, i) => i + 1);
};
