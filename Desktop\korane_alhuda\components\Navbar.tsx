
import React from 'react';
import { AppSection } from '../types';
import { 
  BookOpenIcon, ClockIcon, HandRaisedIcon, ScrollIcon, SparklesIcon, CogIcon, 
  CpuChipIcon, HeartIcon, XMarkIcon 
} from './icons';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  activeSection: AppSection;
  setActiveSection: (section: AppSection) => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose, activeSection, setActiveSection }) => {
  const navItems = [
    { section: AppSection.PrayerTimes, label: 'الصلاة', IconComponent: ClockIcon },
    { section: AppSection.Quran, label: 'القرآن', IconComponent: BookOpenIcon },
    { section: AppSection.Hadith, label: 'الحديث', IconComponent: ScrollIcon },
    { section: AppSection.AsmaulHusna, label: 'أسماء الله', IconComponent: HeartIcon },
    { section: AppSection.Adhkar, label: 'الأذكار', IconComponent: SparklesIcon },
    { section: AppSection.AiDuaGenerator, label: 'مولد الأدعية', IconComponent: CpuChipIcon },
    { section: AppSection.Tasbih, label: 'التسبيح', IconComponent: HandRaisedIcon },
    { section: AppSection.Settings, label: 'الإعدادات', IconComponent: CogIcon }
  ];

  return (
    <aside 
      className={`fixed top-0 ${document.documentElement.dir === 'rtl' ? 'right-0' : 'left-0'} 
                 h-full w-72 bg-card-bg shadow-2xl z-50 
                 transform transition-transform duration-300 ease-in-out
                 ${isOpen ? 'translate-x-0' : (document.documentElement.dir === 'rtl' ? 'translate-x-full' : '-translate-x-full')}
                 flex flex-col`}
      aria-label="القائمة الرئيسية"
      role="navigation"
    >
      <div className="flex justify-between items-center p-4 border-b border-border-default">
        <h2 className="text-xl font-semibold text-primary">القائمة</h2>
        <button 
          onClick={onClose} 
          className="p-2 text-text-dark hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary-light rounded-md"
          aria-label="إغلاق القائمة"
        >
          <XMarkIcon className="w-6 h-6" />
        </button>
      </div>

      <nav className="flex-grow p-4 space-y-3 overflow-y-auto">
        {navItems.map(item => {
          const Icon = item.IconComponent;
          const isActive = activeSection === item.section;
          return (
            <button
              key={item.section}
              onClick={() => setActiveSection(item.section)}
              className={`w-full flex items-center p-3 rounded-lg shadow-md transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-primary-light
                ${ isActive 
                    ? 'bg-primary text-white hover:bg-primary-dark' 
                    : 'bg-background-light text-card-text hover:bg-secondary-light hover:text-primary dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
              aria-label={item.label}
              aria-current={isActive ? 'page' : undefined}
            >
              <Icon className={`w-6 h-6 ${document.documentElement.dir === 'rtl' ? 'ml-3' : 'mr-3'}`} />
              <span className="text-sm font-medium">{item.label}</span>
            </button>
          );
        })}
      </nav>
    </aside>
  );
};

// For backward compatibility if Navbar is still imported elsewhere, but should be Sidebar.
export const Navbar: React.FC<SidebarProps> = Sidebar;
