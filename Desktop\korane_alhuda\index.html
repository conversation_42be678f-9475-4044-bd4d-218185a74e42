<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>قرآن الهدى</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Base variables for the light theme (default) */
    :root {
      --color-primary-light: #60a5fa; /* blue-400 */
      --color-primary-default: #3b82f6; /* blue-500 */
      --color-primary-dark: #2563eb;   /* blue-600 */
      
      --color-secondary-light: #e5e7eb; /* gray-200 */
      --color-secondary-default: #d1d5db; /* gray-300 */
      --color-secondary-dark: #9ca3af;    /* gray-400 */
      
      --color-background-light: #ffffff; /* For cards and interactive elements - White */
      --color-background-default: #f3f4f6; /* gray-100 for overall app background - Very Light Gray */
      
      --color-text-default: #1f2937; /* gray-800 for primary text - Dark Gray */
      --color-text-dark: #6b7280;   /* gray-500 for secondary/muted text - Medium Gray */

      --color-accent: #f59e0b; /* amber-500 for accents */
      --color-card-background: var(--color-background-light); /* Explicitly white for cards */
      --color-card-text: var(--color-text-default);
      --color-border-default: #e5e7eb; /* gray-200 - Light Gray for borders */

      --color-error-text: #dc2626; /* red-600 */
      --color-error-background: #fee2e2; /* red-100 */
    }

    html.dark {
      --color-primary-light: #2563eb; /* blue-600 (Darker primary for light variant in dark mode) */
      --color-primary-default: #3b82f6; /* blue-500 (Can remain same or be slightly lighter) */
      --color-primary-dark: #60a5fa;   /* blue-400 (Lighter primary for dark variant in dark mode) */
      
      --color-secondary-light: #4b5563; /* gray-600 */
      --color-secondary-default: #374151; /* gray-700 */
      --color-secondary-dark: #1f2937;    /* gray-800 */
      
      --color-background-light: #374151; /* gray-700 for cards in dark mode */
      --color-background-default: #111827; /* gray-900 for overall app background in dark mode */
      
      --color-text-default: #f3f4f6; /* gray-100 for primary text in dark mode */
      --color-text-dark: #9ca3af;   /* gray-400 for secondary/muted text in dark mode */

      /* --color-accent: #f59e0b; /* Amber-500 can work in dark mode too, or adjust if needed */
      
      --color-card-background: var(--color-background-light); /* Will use the dark card background */
      --color-card-text: var(--color-text-default);     /* Will use the dark text on cards */
      --color-border-default: #4b5563; /* gray-600 for borders in dark mode */

      --color-error-text: #fca5a5; /* red-300 */
      --color-error-background: #7f1d1d; /* red-900 (darker red background) */
    }

    body {
      font-family: 'Tajawal', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
      background-color: var(--color-background-default); /* Overall app background */
      color: var(--color-text-default);
      direction: rtl; /* Ensure body also has RTL direction */
    }
    .font-quran { 
        font-family: 'Amiri', 'Times New Roman', Times, serif;
    }

    /* Hide scrollbars for a native mobile app feel */
    /* For WebKit browsers (Chrome, Safari, new Edge) */
    ::-webkit-scrollbar {
      display: none;
    }
    /* For Firefox */
    html, body { /* Apply to html and body for broad coverage */
      scrollbar-width: none;
    }
    /* For IE and old Edge */
    body {
      -ms-overflow-style: none;
    }
  </style>
  <script>
    tailwind.config = {
      darkMode: 'class', // Enable class strategy for dark mode
      theme: {
        extend: {
          fontFamily: {
            sans: ['Tajawal', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', 'Helvetica', 'Arial', 'sans-serif', '"Apple Color Emoji"', '"Segoe UI Emoji"', '"Segoe UI Symbol"'],
            quran: ['Amiri', '"Times New Roman"', 'Times', 'serif'],
          },
          colors: {
            primary: {
              light: 'var(--color-primary-light)',
              DEFAULT: 'var(--color-primary-default)',
              dark: 'var(--color-primary-dark)'
            },
            secondary: {
              light: 'var(--color-secondary-light)',
              DEFAULT: 'var(--color-secondary-default)',
              dark: 'var(--color-secondary-dark)'
            },
            background: {
              light: 'var(--color-background-light)', // For cards, specific white backgrounds
              DEFAULT: 'var(--color-background-default)', // For main app body/page background
            },
            text: {
              DEFAULT: 'var(--color-text-default)', // Standard text
              dark: 'var(--color-text-dark)',    // For secondary/muted text
            },
            accent: 'var(--color-accent)',
            'card-bg': 'var(--color-card-background)', // Card background (white or dark card bg)
            'card-text': 'var(--color-card-text)',     // Text on cards
            'border-default': 'var(--color-border-default)',
            error: {
              text: 'var(--color-error-text)',
              background: 'var(--color-error-background)'
            }
          }
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.5.1",
    "@capacitor/cli": "https://esm.sh/@capacitor/cli@^7.4.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-background-default text-text-default"> 
  <div id="root"></div>
  <script type="module" src="./index.tsx"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>