export enum AppSection {
  PrayerTimes = 'PrayerTimes',
  Quran = 'Quran',
  Tasbih = 'Tasbih',
  Hadith = 'Hadith',
  <PERSON><PERSON><PERSON> = '<PERSON><PERSON><PERSON>', // Stays as AI Adhkar Generator for now
  AiDuaGenerator = 'AiDuaGenerator',
  AsmaulHusna = 'AsmaulHusna', // New section for Names of Allah
  Settings = 'Settings',
}

export type QuranViewMode = 'surahList' | 'ayahList' | 'searchResults' | 'mushafView';


export interface PrayerTimes {
  Fajr: string;
  Dhuhr: string;
  Asr: string;
  Maghrib: string;
  Isha: string;
  Sunrise?: string;
  Sunset?: string;
  Imsak?: string;
  Midnight?: string;
}

export interface AlAdhanApiResponse {
  code: number;
  status: string;
  data: AlAdhanData;
}

export interface AlAdhanCalculationMethod {
  id: number;
  name: string;
  params?: Record<string, any>; 
  location?: { 
    latitude: number;
    longitude: number;
  };
}

export interface AlAdhanData {
  timings: PrayerTimes;
  date: {
    readable: string;
    hijri: HijriDate;
    gregorian: GregorianDate;
  };
  meta: {
    latitude: number;
    longitude: number;
    timezone: string;
    method: AlAdhanCalculationMethod;
    latitudeAdjustmentMethod: string;
    midnightMode: string;
    school: 'standard' | 'hanafi'; 
    offset: Record<string, number>;
  };
}

export interface HijriDate {
  date: string;
  format: string;
  day: string;
  weekday: { en: string; ar: string };
  month: { number: number; en: string; ar: string };
  year: string;
  designation: { abbreviated: string; expanded: string };
  holidays: string[];
}

export interface GregorianDate {
    date: string;
    format: string;
    day: string;
    weekday: { en: string };
    month: { number: number; en: string };
    year: string;
    designation: { abbreviated: string; expanded: string };
}


export interface QuranAyahData { 
  number: number; 
  text: string;
  edition: QuranEdition;
  surah: { 
    number: number;
    name: string; 
    englishName: string;
    englishNameTranslation: string;
    revelationType: 'Meccan' | 'Medinan';
    numberOfAyahs: number;
  };
  numberInSurah: number;
  juz: number;
  manzil: number;
  page: number;
  ruku: number;
  hizbQuarter: number;
  sajda: boolean | { id: number; recommended: boolean; obligatory: boolean };
}

export interface QuranVerseApiResponse { 
  code: number;
  status: string;
  data: QuranAyahData; 
}

export interface QuranEdition {
  identifier: string;
  language: string;
  name: string;
  englishName: string;
  format: string;
  type: string; 
  direction: string; 
}

export interface SurahMetadata {
  number: number;
  name: string; 
  englishName: string;
  englishNameTranslation: string;
  numberOfAyahs: number;
  revelationType: 'Meccan' | 'Medinan';
}

export interface SurahListApiResponse {
  code: number;
  status: string;
  data: SurahMetadata[];
}

export interface EditionAyah {
  number: number; 
  text: string; 
  arabicText?: string; 
  translationText?: string; 
  numberInSurah: number;
  juz?: number;
  manzil?: number;
  page?: number;
  ruku?: number;
  hizbQuarter?: number;
  sajda?: boolean | any; 
  audio?: string; 
  audioSecondary?: string[];
  tafsir?: string | null; 
  showingTafsir?: boolean;
  audioPlaying?: boolean;
}

export interface SurahFullEditionData {
  number: number;
  name: string; 
  englishName: string;
  englishNameTranslation: string;
  revelationType: 'Meccan' | 'Medinan';
  numberOfAyahs: number;
  ayahs: EditionAyah[]; 
  edition: QuranEdition;
}

export interface FullSurahApiResponse {
  code: number;
  status: string;
  data: SurahFullEditionData[]; 
}

export interface AyahTafsirData extends QuranAyahData {}

export interface AyahTafsirApiResponse {
  code: number;
  status: string;
  data: AyahTafsirData;
}

export interface QuranSearchMatch {
  surah: number;
  ayah: number;
  text: string; 
}
export interface QuranSearchResultItem {
  number: number; 
  text: string; 
  edition: QuranEdition;
  surah: {
    number: number;
    name: string; 
    englishName: string;
    numberOfAyahs: number;
  };
  numberInSurah: number;
}
export interface QuranSearchApiResponse {
  code: number;
  status: string;
  data: {
    count: number;
    matches: QuranSearchResultItem[]; 
  };
}

export interface TranslationOption {
  identifier: string;
  name: string; 
  language: string; 
}

export interface HadithBook {
  id: number;
  bookName: string; 
  writerName: string;
  writerDeath: string;
  aboutWriter: string;
  bookNameArabic?: string; 
  collection?: Array<{ name: string; slug: string; lang: string }>; 
  hadiths_count?: number; 
  chapters_count?: number; 
}

export interface HadithBookListApiResponse {
  books: HadithBook[]; 
}

export interface HadithItem {
  id: number;
  hadithNumber: string;
  englishNarrator?: string; 
  hadithEnglish?: string;
  hadithUrdu?: string; 
  hadithArabic?: string;
  chapterId?: number;
  bookId?: number;
  chapter?: {
    id: number;
    chapterNumber: string;
    chapterEnglish?: string;
    chapterArabic?: string;
  };
  book?: {
    id: number;
    bookName: string; 
    writerName?: string;
  };
  grade?: string; 
  volume?: string; 
}

export interface HadithListApiResponse {
  hadiths: {
    data: HadithItem[];
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
  book?: HadithBook; 
  chapter?: any; 
}

export interface DhikrItem { // Old, potentially for Adhkar section if it was static
  id: string; 
  arabic: string;
  translation: string;
  reference?: string; 
  count?: number | string; 
  virtue?: string; 
}

export interface DhikrCategory { // Old
  id: string; 
  name: string; 
  items: DhikrItem[];
}

// Settings Types
export interface PrayerTimesSettings {
  calculationMethodId: number;
  asrJuristicMethod: 'standard' | 'hanafi';
  fajrOffset: number;
  dhuhrOffset: number;
  asrOffset: number;
  maghribOffset: number;
  ishaOffset: number;
}

export interface MushafViewSettings {
  currentPage: number;
  bookmarkedPages: number[];
}

export interface QuranSettings {
  defaultTranslation: string; 
  arabicFontSize: 'small' | 'medium' | 'large';
  translationFontSize: 'small' | 'medium' | 'large';
  mushafViewSettings: MushafViewSettings;
}

export interface GeneralSettings {
  appLanguage: 'en' | 'ar'; 
  themeMode: 'light' | 'dark';
}

export interface AppSettings {
  prayerTimes: PrayerTimesSettings;
  quran: QuranSettings;
  general: GeneralSettings;
}

export interface PrayerCalculationMethod {
  id: number;
  name: string;
}

export interface AsrJuristicOption {
  id: 'standard' | 'hanafi';
  name: string;
}

// New types for added features
export interface DailyReminder {
  id: string; // e.g., "ayah_baqarah_255" or "hadith_bukhari_1"
  type: 'ayah' | 'hadith';
  textAr: string;
  sourceAr: string; // e.g., "سورة البقرة: ٢٥٥" or "صحيح البخاري، رقم ١"
  // Optional: translation if available
  textEn?: string; 
  sourceEn?: string;
}

export interface AllahsName {
  id: number;
  nameAr: string;        // الاسم بالعربي e.g., "الرحمن"
  nameTransliteration: string; // e.g., "Ar-Rahmaan"
  meaningAr: string;     // المعنى بالعربي e.g., "الواسع الرحمة"
  meaningEn?: string;    // Optional: English meaning for reference or future use
}

export interface DhikrInSequence {
  id: string; // unique ID for this dhikr in sequence, e.g., "subhanallah_post_prayer"
  name: string; // Display name, e.g., "التسبيح"
  arabic: string; // The actual dhikr text, e.g., "سبحان الله"
  target: number;
}

export interface TasbihPreset {
  id: string; // Unique ID for the preset, e.g., "subhanallah_33" or "post_prayer_adhkar"
  name: string; // Display name for the dropdown
  target?: number; // For simple, single tasbihs. Becomes target of the single dhikr in sequence if sequence has 1 item.
  sequence?: DhikrInSequence[]; // For sequenced Adhkar. If present, this takes precedence.
}