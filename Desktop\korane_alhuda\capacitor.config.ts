
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.quranalhuda.app',
  appName: 'قرآن الهدى',
  webDir: 'www',
  plugins: {
    SplashScreen: {
      launchShowDuration: 0 // Handled by custom splash screen in App.tsx
    }
  },
  server: {
    androidScheme: 'http' // Can help with some local asset loading issues if they arise
  }
};

export default config;