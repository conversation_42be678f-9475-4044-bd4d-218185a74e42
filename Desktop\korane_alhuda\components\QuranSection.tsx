
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  fetchAllSurahsMetadata, 
  fetchFullSurahWithEditions, 
  fetchAyahTafsir,
  searchQuran
} from '../services/quranService';
import { 
  SurahMetadata, 
  EditionAyah, 
  SurahFullEditionData, 
  TranslationOption, 
  AyahTafsirData,
  QuranSearchResultItem,
  QuranSettings,
  QuranViewMode 
} from '../types';
import { 
  QURAN_ARABIC_EDITION, 
  AVAILABLE_TRANSLATIONS,
  QURAN_AUDIO_EDITION,
  QURAN_TAFSIR_EDITION,
  TOTAL_MUSHAF_PAGES
} from '../constants';
import { surahStartPages, getAllSurahNumbersForMushaf } from '../data/surahStartPages';
import { juzStartPages, getAllJuzNumbersForMushaf } from '../data/juzStartPages';
import { Spinner } from './Spinner';
import { BackButton } from './BackButton';
import { 
  BookOpenIcon, 
  PlayIcon, 
  PauseIcon, 
  ChatBubbleBottomCenterTextIcon,
  MagnifyingGlassIcon,
  ArrowRightIcon as NavArrowRightIcon, 
  ArrowLeftIcon as NavArrowLeftIcon,   
  ChevronDownIcon,
  PhotoIcon,
  BookmarkIcon,
  BookmarkSlashIcon,
  QueueListIcon,
  CogIcon, 
  XMarkIcon 
} from './icons';

interface DisplaySurahData {
  metadata: SurahMetadata;
  ayahs: EditionAyah[]; 
}

interface QuranSectionProps {
  quranSettings: QuranSettings;
  onUpdateQuranSettings: (newSettings: Partial<QuranSettings>) => void;
  onSetAppHeaderVisibility: (visible: boolean) => void;
}

const SWIPE_THRESHOLD = 50; // Minimum pixels for a swipe to be registered

export const QuranSection: React.FC<QuranSectionProps> = ({ quranSettings, onUpdateQuranSettings, onSetAppHeaderVisibility }) => {
  const [viewMode, setViewMode] = useState<QuranViewMode>('surahList');
  const [surahs, setSurahs] = useState<SurahMetadata[]>([]);
  const [selectedSurahData, setSelectedSurahData] = useState<DisplaySurahData | null>(null);
  const [currentSurahNumberForAyahView, setCurrentSurahNumberForAyahView] = useState<number | null>(null);
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const [selectedTranslation, setSelectedTranslation] = useState<TranslationOption>(
    AVAILABLE_TRANSLATIONS.find(t => t.identifier === quranSettings.defaultTranslation) || AVAILABLE_TRANSLATIONS[0]
  );

  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);
  const [currentlyPlayingAyahNumber, setCurrentlyPlayingAyahNumber] = useState<number | null>(null);
  const [ayahTafsirsLoading, setAyahTafsirsLoading] = useState<Record<number, boolean>>({});

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchEdition, setSearchEdition] = useState<string>(quranSettings.defaultTranslation);
  const [searchResults, setSearchResults] = useState<QuranSearchResultItem[]>([]);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Mushaf View State
  const [mushafCurrentPage, setMushafCurrentPage] = useState<number>(quranSettings.mushafViewSettings.currentPage);
  const [mushafBookmarkedPages, setMushafBookmarkedPages] = useState<number[]>(quranSettings.mushafViewSettings.bookmarkedPages);
  const [showBookmarksList, setShowBookmarksList] = useState<boolean>(false);
  const [pageInput, setPageInput] = useState<string>(quranSettings.mushafViewSettings.currentPage.toString());
  const [selectedSurahForMushaf, setSelectedSurahForMushaf] = useState<number>(0); 
  const [selectedJuzForMushaf, setSelectedJuzForMushaf] = useState<number>(0); 
  const [isMushafSettingsPanelOpen, setIsMushafSettingsPanelOpen] = useState<boolean>(false);
  const touchStartX = useRef<number | null>(null);


  useEffect(() => {
    const currentDefault = AVAILABLE_TRANSLATIONS.find(t => t.identifier === quranSettings.defaultTranslation) || AVAILABLE_TRANSLATIONS[0];
    setSelectedTranslation(currentDefault);
    if (searchEdition !== QURAN_ARABIC_EDITION && searchEdition !== currentDefault.identifier) {
        setSearchEdition(currentDefault.identifier);
    }
    setMushafCurrentPage(quranSettings.mushafViewSettings.currentPage);
    setPageInput(quranSettings.mushafViewSettings.currentPage.toString());
    setMushafBookmarkedPages(quranSettings.mushafViewSettings.bookmarkedPages);

  }, [quranSettings]);


  const updateMushafSettings = (newPage: number, newBookmarks?: number[]) => {
    const updatedBookmarks = newBookmarks !== undefined ? newBookmarks : mushafBookmarkedPages;
    onUpdateQuranSettings({
      mushafViewSettings: {
        currentPage: newPage,
        bookmarkedPages: updatedBookmarks,
      },
    });
  };

  const getFontSizeClass = (type: 'arabic' | 'translation'): string => {
    const size = type === 'arabic' ? quranSettings.arabicFontSize : quranSettings.translationFontSize;
    switch(size) {
      case 'small': return 'text-sm md:text-base'; 
      case 'medium': return type === 'arabic' ? 'text-xl md:text-2xl' : 'text-base md:text-lg'; 
      case 'large': return type === 'arabic' ? 'text-2xl md:text-3xl' : 'text-lg md:text-xl';
      default: return type === 'arabic' ? 'text-xl md:text-2xl' : 'text-base md:text-lg';
    }
  };
  
  const loadSurahDataForAyahView = useCallback(async (surahNumber: number, translationId: string) => {
    setLoading(true);
    setError(null);
    setSelectedSurahData(null); 
    try {
      const editionsData = await fetchFullSurahWithEditions(surahNumber, QURAN_ARABIC_EDITION, translationId, true);
      const arabicEdition = editionsData.find(e => e.edition.identifier === QURAN_ARABIC_EDITION);
      const currentTranslationEdition = editionsData.find(e => e.edition.identifier === translationId);
      const audioEditionData = editionsData.find(e => e.edition.identifier === QURAN_AUDIO_EDITION);

      if (!arabicEdition || !currentTranslationEdition) {
        throw new Error('النسخ المطلوبة (العربية أو الترجمة) غير موجودة.');
      }
      
      const combinedAyahs = arabicEdition.ayahs.map((arabAyah) => {
          const transAyah = currentTranslationEdition.ayahs.find(t => t.numberInSurah === arabAyah.numberInSurah);
          const audioAyah = audioEditionData?.ayahs.find(a => a.numberInSurah === arabAyah.numberInSurah);
          const existingAyahState = selectedSurahData?.ayahs.find(a => a.number === arabAyah.number);
          return {
              ...arabAyah, 
              arabicText: arabAyah.text,
              translationText: transAyah?.text || "الترجمة غير متوفرة.",
              audio: audioAyah?.audio,
              audioSecondary: audioAyah?.audioSecondary,
              tafsir: existingAyahState?.tafsir, 
              showingTafsir: existingAyahState?.showingTafsir || false,
              audioPlaying: currentlyPlayingAyahNumber === arabAyah.number,
          };
      });
      const surahMeta = surahs.find(s => s.number === surahNumber) || 
        { number: arabicEdition.number, name: arabicEdition.name, englishName: arabicEdition.englishName, englishNameTranslation: arabicEdition.englishNameTranslation, numberOfAyahs: arabicEdition.numberOfAyahs, revelationType: arabicEdition.revelationType };
      setSelectedSurahData({ metadata: surahMeta, ayahs: combinedAyahs });
      setCurrentSurahNumberForAyahView(surahNumber);
      setViewMode('ayahList');
    } catch (err) {
      setError(err instanceof Error ? err.message : `فشل تحميل آيات سورة ${surahNumber}.`);
    } finally {
      setLoading(false);
    }
  }, [surahs, currentlyPlayingAyahNumber, selectedSurahData?.ayahs]); 

  const loadAllSurahs = useCallback(async () => {
    if (surahs.length > 0 && (viewMode === 'surahList' || viewMode === 'mushafView')) { 
        setLoading(false);
        return;
    }
    setLoading(true);
    setError(null);
    try {
      const surahList = await fetchAllSurahsMetadata();
      setSurahs(surahList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'فشل تحميل قائمة السور.');
    } finally {
      setLoading(false);
    }
  }, [surahs.length, viewMode]);

  useEffect(() => {
    if (viewMode === 'surahList' || viewMode === 'mushafView') {
      loadAllSurahs();
    }
  }, [viewMode, loadAllSurahs]);
  
  useEffect(() => {
    // Control app header visibility based on Quran view mode
    if (viewMode === 'mushafView') {
      onSetAppHeaderVisibility(false);
    } else {
      onSetAppHeaderVisibility(true);
    }
    // Cleanup function to restore header if component unmounts while in mushafView
    return () => {
      onSetAppHeaderVisibility(true);
    };
  }, [viewMode, onSetAppHeaderVisibility]);


  const handleSurahSelectForAyahView = (surah: SurahMetadata) => {
    loadSurahDataForAyahView(surah.number, selectedTranslation.identifier);
  };
  
  const handleTranslationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newTranslationId = event.target.value;
    const newTrans = AVAILABLE_TRANSLATIONS.find(t => t.identifier === newTranslationId) || selectedTranslation;
    setSelectedTranslation(newTrans);
    if (currentSurahNumberForAyahView && viewMode === 'ayahList') {
      loadSurahDataForAyahView(currentSurahNumberForAyahView, newTranslationId);
    }
    if (searchEdition !== QURAN_ARABIC_EDITION && searchEdition !== newTranslationId) {
        setSearchEdition(newTranslationId);
    }
  };

  const playAudio = (ayah: EditionAyah) => {
    if (!ayah.audio) return;
    if (audioPlayerRef.current && currentlyPlayingAyahNumber === ayah.number) { 
        if (audioPlayerRef.current.paused) { audioPlayerRef.current.play(); setCurrentlyPlayingAyahNumber(ayah.number); } 
        else { audioPlayerRef.current.pause(); setCurrentlyPlayingAyahNumber(null); }
    } else { 
        if (audioPlayerRef.current) { audioPlayerRef.current.pause(); }
        audioPlayerRef.current = new Audio(ayah.audio);
        audioPlayerRef.current.play().then(() => setCurrentlyPlayingAyahNumber(ayah.number))
            .catch(err => { console.error("خطأ في تشغيل الصوت:", err); setError(`تعذر تشغيل صوت الآية ${ayah.numberInSurah}.`); setCurrentlyPlayingAyahNumber(null); });
        audioPlayerRef.current.onended = () => setCurrentlyPlayingAyahNumber(null);
    }
  };
  
 useEffect(() => {
    if (selectedSurahData) {
        setSelectedSurahData(prev => {
            if (!prev) return null;
            const needsUpdate = prev.ayahs.some(a => a.audioPlaying !== (a.number === currentlyPlayingAyahNumber));
            if (needsUpdate) { return { ...prev, ayahs: prev.ayahs.map(a => ({ ...a, audioPlaying: a.number === currentlyPlayingAyahNumber })) }; }
            return prev;
        });
    }
}, [currentlyPlayingAyahNumber, selectedSurahData?.metadata.number ]);

  const toggleTafsir = async (ayahNumberInQuran: number, ayahIndex: number) => {
    if (!selectedSurahData) return;
    const currentAyah = selectedSurahData.ayahs[ayahIndex];
    const newShowingTafsirState = !currentAyah.showingTafsir;
    setSelectedSurahData(prev => {
        if (!prev) return null;
        const updatedAyahs = [...prev.ayahs];
        updatedAyahs[ayahIndex] = { ...updatedAyahs[ayahIndex], showingTafsir: newShowingTafsirState };
        return { ...prev, ayahs: updatedAyahs };
    });
    if (newShowingTafsirState && !currentAyah.tafsir) { 
        setAyahTafsirsLoading(prev => ({ ...prev, [ayahNumberInQuran]: true }));
        try {
          const tafsirData: AyahTafsirData = await fetchAyahTafsir(ayahNumberInQuran, QURAN_TAFSIR_EDITION);
          setSelectedSurahData(prev => {
            if (!prev) return null;
            const finalAyahs = prev.ayahs.map((ayah, idx) => idx === ayahIndex ? { ...ayah, tafsir: tafsirData.text, showingTafsir: true } : ayah);
            return { ...prev, ayahs: finalAyahs };
          });
        } catch (err) {
          const errorMsg = err instanceof Error ? err.message : `فشل تحميل التفسير للآية.`;
          setSelectedSurahData(prev => {
            if (!prev) return null;
            const finalAyahs = prev.ayahs.map((ayah, idx) => idx === ayahIndex ? { ...ayah, tafsir: errorMsg, showingTafsir: true } : ayah);
            return { ...prev, ayahs: finalAyahs };
          });
        } finally {
          setAyahTafsirsLoading(prev => ({ ...prev, [ayahNumberInQuran]: false }));
        }
    }
  };
  
  const handleSearch = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!searchQuery.trim()) { setSearchResults([]); setSearchError(null); setViewMode('surahList'); return; }
    setSearchLoading(true); setSearchError(null); setSearchResults([]); 
    try {
        const response = await searchQuran(searchQuery, searchEdition);
        setSearchResults(response.data.matches);
        setViewMode('searchResults'); 
    } catch (err) {
        setSearchError(err instanceof Error ? err.message : "فشل البحث.");
        setSearchResults([]); setViewMode('searchResults'); 
    } finally {
        setSearchLoading(false);
    }
  };

  const navigateToAyahFromSearch = (surahNumber: number, ayahNumberInSurah: number) => {
     const targetSurah = surahs.find(s => s.number === surahNumber);
     if (targetSurah) {
        loadSurahDataForAyahView(surahNumber, selectedTranslation.identifier).then(() => {
          setTimeout(() => {
            const ayahElement = document.getElementById(`ayah-${surahNumber}-${ayahNumberInSurah}`);
            ayahElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }, 100);
        });
     } else { setError(`تعذر العثور على سورة ${surahNumber} للانتقال إليها.`); }
  };

  const navigateSurahInAyahView = (direction: 'next' | 'prev') => {
    if (!currentSurahNumberForAyahView) return;
    let targetSurahNumber = direction === 'next' ? currentSurahNumberForAyahView + 1 : currentSurahNumberForAyahView - 1;
    if (targetSurahNumber < 1) targetSurahNumber = 114; 
    if (targetSurahNumber > 114) targetSurahNumber = 1; 
    const targetSurah = surahs.find(s => s.number === targetSurahNumber);
    if (targetSurah) { loadSurahDataForAyahView(targetSurah.number, selectedTranslation.identifier); }
  };

  const handleBackToQuranHome = () => { 
    onSetAppHeaderVisibility(true); // Restore app header
    setViewMode('surahList');
    setSelectedSurahData(null);
    setCurrentSurahNumberForAyahView(null);
    setError(null); 
    setSearchResults([]);
    setSearchQuery(''); 
    setSearchError(null); 
    setShowBookmarksList(false);
    setIsMushafSettingsPanelOpen(false); 
  };

  // Mushaf View Handlers
  const handleMushafPageChange = (newPage: number) => {
    const pageNum = Math.max(1, Math.min(TOTAL_MUSHAF_PAGES, newPage));
    setMushafCurrentPage(pageNum);
    setPageInput(pageNum.toString());
    updateMushafSettings(pageNum);
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value);
  };

  const handleGoToPage = () => {
    const pageNum = parseInt(pageInput, 10);
    if (!isNaN(pageNum)) {
      handleMushafPageChange(pageNum);
    }
  };
  
  const handleSurahSelectForMushaf = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const surahNum = parseInt(e.target.value, 10);
    setSelectedSurahForMushaf(surahNum);
    if (surahNum > 0 && surahStartPages[surahNum]) {
      handleMushafPageChange(surahStartPages[surahNum]);
    }
  };

  const handleJuzSelectForMushaf = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const juzNum = parseInt(e.target.value, 10);
    setSelectedJuzForMushaf(juzNum);
    if (juzNum > 0 && juzStartPages[juzNum]) {
      handleMushafPageChange(juzStartPages[juzNum]);
    }
  };

  const toggleBookmark = (page: number) => {
    let newBookmarks;
    if (mushafBookmarkedPages.includes(page)) {
      newBookmarks = mushafBookmarkedPages.filter(p => p !== page);
    } else {
      newBookmarks = [...mushafBookmarkedPages, page].sort((a,b) => a-b);
    }
    setMushafBookmarkedPages(newBookmarks);
    updateMushafSettings(mushafCurrentPage, newBookmarks);
  };

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    if (touchStartX.current === null) return;
    const touchEndX = e.changedTouches[0].clientX;
    const deltaX = touchEndX - touchStartX.current;

    if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
      if (deltaX > 0) { // Swiped Right (for RTL, this means next page - page number decreases)
        if (mushafCurrentPage > 1) handleMushafPageChange(mushafCurrentPage - 1);
      } else { // Swiped Left (for RTL, this means previous page - page number increases)
        if (mushafCurrentPage < TOTAL_MUSHAF_PAGES) handleMushafPageChange(mushafCurrentPage + 1);
      }
    }
    touchStartX.current = null;
  };
  
  const renderSurahList = () => (
    <>
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
        <div className="flex items-center justify-center mb-4">
            <button 
                onClick={() => {
                  onSetAppHeaderVisibility(false);
                  setViewMode('mushafView');
                  loadAllSurahs(); // Ensure surahs are loaded for dropdowns in panel
                }}
                className="bg-primary-light text-primary-dark hover:bg-primary hover:text-white px-4 py-2 rounded-md transition duration-150 flex items-center text-sm"
            >
                <PhotoIcon className="w-5 h-5 ms-2"/> عرض المصحف (صفحات)
            </button>
        </div>
        <h2 className="text-3xl font-bold text-primary mb-2 flex items-center justify-center">
          <BookOpenIcon className="w-8 h-8 ms-3"/> تصفح القرآن (نصي)
        </h2>
        <p className="text-text-dark">اختر سورة للقراءة أو ابحث في القرآن.</p>
      </div>
      {renderSearchBar()}
      {error && <p className="text-center text-error-text bg-error-background p-3 rounded-md">{error}</p>}
      {searchError && viewMode === 'surahList' && <p className="text-center text-error-text bg-error-background p-3 rounded-md my-4">{searchError}</p>}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {surahs.map(surah => (
          <button
            key={surah.number}
            onClick={() => handleSurahSelectForAyahView(surah)}
            className="p-4 bg-card-bg rounded-xl shadow-md hover:shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 text-start focus:outline-none focus:ring-2 focus:ring-primary-light focus:ring-opacity-60"
            aria-label={`اختر سورة ${surah.englishName}`}
          >
            <div className="flex justify-between items-center">
              <span className="text-xs px-2 py-1 bg-primary-light text-primary-dark rounded-full">
                {surah.number}
              </span>
              <span className="text-sm text-text-dark">{surah.revelationType === "Meccan" ? "مكية" : "مدنية"}</span>
            </div>
            <h3 className="mt-2 text-xl font-semibold text-primary font-quran" dir="rtl">{surah.name}</h3>
            <p className="text-card-text">{surah.englishName}</p>
            <p className="text-sm text-text-dark">{surah.englishNameTranslation}</p>
            <p className="mt-1 text-xs text-text-dark">{surah.numberOfAyahs} آيات</p>
          </button>
        ))}
      </div>
    </>
  );

  const renderAyahList = () => {
    if (!selectedSurahData) return <div className="flex justify-center items-center h-64"><Spinner /> <p className="me-2 text-text-default">جاري تحميل محتوى السورة...</p></div>;
    return (
    <>
      <div className="p-4 sm:p-6 bg-card-bg rounded-2xl shadow-lg">
        <div className="flex justify-between items-center mb-4">
            <BackButton onClick={handleBackToQuranHome} label="الرجوع إلى قائمة السور" />
            <div className="relative">
                <select 
                    value={selectedTranslation.identifier} 
                    onChange={handleTranslationChange}
                    className="appearance-none bg-background-light border border-border-default text-text-default py-2 ps-4 pe-8 rounded-md leading-tight focus:outline-none focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light"
                >
                    {AVAILABLE_TRANSLATIONS.map(trans => (
                        <option key={trans.identifier} value={trans.identifier}>{trans.name}</option>
                    ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-2 text-text-dark"> 
                    <ChevronDownIcon className="w-4 h-4"/>
                </div>
            </div>
        </div>
        <div className="text-center mb-6">
          <h2 className="text-3xl font-bold text-primary font-quran" dir="rtl">{selectedSurahData.metadata.name}</h2>
          <p className="text-xl text-card-text">{selectedSurahData.metadata.englishName}</p>
          <p className="text-md text-text-dark">{selectedSurahData.metadata.englishNameTranslation}</p>
          <p className="text-sm text-text-dark">
            {selectedSurahData.metadata.revelationType === "Meccan" ? "مكية" : "مدنية"} - {selectedSurahData.metadata.numberOfAyahs} آيات
          </p>
        </div>
        <div className="flex justify-between items-center my-4">
             <button onClick={() => navigateSurahInAyahView('prev')} className="p-2 bg-secondary-default rounded-full hover:bg-secondary-dark transition active:scale-95 transform transition-transform duration-75">
                <NavArrowRightIcon className="w-6 h-6 text-text-dark hover:text-text-default"/>
            </button>
            <span className="text-sm text-text-dark">سورة {selectedSurahData.metadata.number} / 114</span>
            <button onClick={() => navigateSurahInAyahView('next')} className="p-2 bg-secondary-default rounded-full hover:bg-secondary-dark transition active:scale-95 transform transition-transform duration-75">
                <NavArrowLeftIcon className="w-6 h-6 text-text-dark hover:text-text-default"/>
            </button>
        </div>
        {loading && <div className="flex justify-center items-center h-32"><Spinner /> <p className="me-2 text-text-default">جاري تحميل الآيات...</p></div>}
        {error && <p className="text-center text-error-text bg-error-background p-3 rounded-md my-4">{error}</p>}
        {!loading && !error && (
          <div className="space-y-5">
            {selectedSurahData.ayahs.map((ayah, index) => (
              <div key={ayah.numberInSurah} id={`ayah-${selectedSurahData.metadata.number}-${ayah.numberInSurah}`} className="p-4 border border-border-default rounded-xl bg-background-light shadow-md hover:shadow-lg transition-shadow duration-300 relative">
                <p className="text-sm font-semibold text-primary mb-2"> آية {ayah.numberInSurah} (رقم الآية في المصحف: {ayah.number}) </p>
                <p className={`font-quran text-end leading-loose text-text-default mb-3 ${getFontSizeClass('arabic')}`} dir="rtl"> {ayah.arabicText || ayah.text} </p>
                {ayah.translationText && <p className={`text-text-dark leading-relaxed mb-3 text-start ${getFontSizeClass('translation')}`} dir="ltr"> {ayah.translationText} </p>}
                <div className="flex items-center space-x-2 space-x-reverse mt-2">
                    {ayah.audio && ( <button onClick={() => playAudio(ayah)} className={`p-2 rounded-full hover:bg-secondary-default transition active:scale-95 transform transition-transform duration-75 ${ayah.audioPlaying ? 'bg-primary-light text-primary' : 'bg-secondary-light'}`} aria-label={ayah.audioPlaying ? "إيقاف التلاوة" : "تشغيل التلاوة"} > {ayah.audioPlaying ? <PauseIcon className="w-5 h-5 text-primary"/> : <PlayIcon className="w-5 h-5 text-text-dark"/>} </button> )}
                    <button onClick={() => toggleTafsir(ayah.number, index)} className={`p-2 rounded-full hover:bg-secondary-default transition active:scale-95 transform transition-transform duration-75 ${ayah.showingTafsir ? 'bg-primary-light text-primary' : 'bg-secondary-light'}`} aria-label={ayah.showingTafsir ? "إخفاء التفسير" : "إظهار التفسير"} > <ChatBubbleBottomCenterTextIcon className={`w-5 h-5 ${ayah.showingTafsir ? 'text-primary' : 'text-text-dark'}`}/> </button>
                </div>
                {ayahTafsirsLoading[ayah.number] && <div className="mt-2 flex items-center"><Spinner/> <span className="me-1 text-sm text-text-dark">جاري تحميل التفسير...</span></div>}
                {ayah.showingTafsir && ayah.tafsir && !ayahTafsirsLoading[ayah.number] && ( <div className="mt-3 p-3 bg-secondary-light rounded text-text-default prose prose-sm max-w-none">  <h4 className="font-semibold text-sm text-primary text-start">تفسير ابن كثير (انجليزي):</h4> <p className="text-xs text-text-default leading-relaxed text-start" dir="ltr">{ayah.tafsir}</p> </div> )}
              </div>
            ))}
          </div>
        )}
      </div>
    </>
    );
  };

  const renderSearchBar = () => (
    <form onSubmit={handleSearch} className="my-6 p-4 bg-card-bg rounded-2xl shadow-lg flex flex-col sm:flex-row gap-3 items-center">
        <input type="text" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} placeholder="ابحث في القرآن (مثال: الجنة، الصلاة، محمد)" className="flex-grow p-3 border border-border-default rounded-md focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light bg-background-light text-text-default" />
        <div className="relative w-full sm:w-auto">
            <select value={searchEdition} onChange={(e) => setSearchEdition(e.target.value)} className="appearance-none w-full bg-background-light border border-border-default text-text-default py-3 ps-4 pe-8 rounded-md leading-tight focus:outline-none focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light" >
                <option value={QURAN_ARABIC_EDITION}>بحث في النص العربي</option>
                {AVAILABLE_TRANSLATIONS.map(trans => ( <option key={`search-${trans.identifier}`} value={trans.identifier}>بحث في {trans.name}</option> ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-2 text-text-dark"> <ChevronDownIcon className="w-4 h-4"/> </div>
        </div>
        <button type="submit" disabled={searchLoading} className="w-full sm:w-auto bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-md transition duration-150 flex items-center justify-center disabled:opacity-70 active:scale-95 transform transition-transform duration-75" > <MagnifyingGlassIcon className="w-5 h-5 ms-2"/> {searchLoading ? 'جاري البحث...' : 'بحث'} </button>
    </form>
  );

  const renderSearchResults = () => (
    <> <div className="p-6 bg-card-bg rounded-2xl shadow-lg"> <BackButton onClick={handleBackToQuranHome} label="الرجوع إلى قائمة السور" className="mb-4" /> <h2 className="text-2xl font-bold text-primary mb-1 text-center">نتائج البحث عن "{searchQuery}"</h2> <p className="text-sm text-center text-text-dark mb-4">تم البحث في: {searchEdition === QURAN_ARABIC_EDITION ? "النص العربي" : AVAILABLE_TRANSLATIONS.find(t=>t.identifier === searchEdition)?.name}</p> {renderSearchBar()}  {searchLoading && <div className="flex justify-center items-center h-32"><Spinner /> <p className="me-2 text-text-default">جاري البحث...</p></div>} {!searchLoading && (searchError || searchResults.length === 0) && ( <div className="text-center text-text-dark py-4 px-3 bg-secondary-light dark:bg-gray-700 rounded-md my-4 shadow"> {searchError ? ( <p className="text-error-text mb-3 text-sm">{searchError}</p> ) : ( <p className="mb-3 text-sm">لم يتم العثور على نتائج للبحث عن "<span className="font-semibold text-text-default">{searchQuery}</span>".</p> )} <h4 className="font-semibold text-text-default mt-4 mb-2">نصائح لتحسين البحث:</h4> <ul className="list-disc list-inside text-xs sm:text-sm text-start mx-auto max-w-md text-text-dark dark:text-gray-300 space-y-1"> <li>جرّب البحث باستخدام كلمة رئيسية واحدة أو كلمتين فقط (مثال: ابحث عن "نور" بدلاً من "نور السماوات").</li> <li>تأكد من اختيار نسخة البحث الصحيحة (النص العربي أو إحدى الترجمات).</li> <li>قد تكون خدمة البحث دقيقة جدًا في مطابقة العبارات، حاول تبسيط عبارتك.</li> <li>إذا كنت تبحث في النص العربي، جرب البحث بدون تشكيل إن أمكن (التطبيق حالياً لا يغير التشكيل).</li> <li>تأكد من الإملاء الصحيح للكلمات.</li> </ul> </div> )} {!searchLoading && !searchError && searchResults.length > 0 && ( <div className="space-y-4"> <p className="text-sm text-text-dark">{searchResults.length} نتيجة (نتائج) موجودة.</p> {searchResults.map((result) => ( <div key={result.number} className="p-4 border border-border-default rounded-xl bg-background-light shadow-md hover:shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 cursor-pointer" onClick={() => navigateToAyahFromSearch(result.surah.number, result.numberInSurah)} role="button" tabIndex={0} onKeyPress={(e) => e.key === 'Enter' && navigateToAyahFromSearch(result.surah.number, result.numberInSurah)} > <p className="text-sm font-semibold text-primary mb-1 text-start"> {result.surah.englishName} (سورة {result.surah.number})، آية {result.numberInSurah} </p> <p className={`leading-relaxed ${result.edition.direction === 'rtl' ? 'font-quran text-end' : 'text-start'} text-text-default`} dir={result.edition.direction || 'auto'}> {result.text} </p> </div> ))} </div> )} </div> </>
  );

  const renderMushafView = () => (
    <div className="fixed inset-0 z-[50] bg-background-default flex flex-col"> {/* Full screen container */}
        
        {/* Floating Action Buttons: Settings Cog and Back to Surah List */}
        <div className="absolute top-4 start-4 z-[55]">
            <button 
                onClick={() => setIsMushafSettingsPanelOpen(true)} 
                className="p-3 bg-black bg-opacity-40 hover:bg-opacity-60 text-white rounded-full shadow-lg transition"
                aria-label="إعدادات المصحف"
            >
                <CogIcon className="w-6 h-6" />
            </button>
        </div>
         <div className="absolute top-4 end-4 z-[55]">
            <BackButton onClick={handleBackToQuranHome} label="القائمة" />
        </div>

        {/* Mushaf Page Display Area (for swipe) */}
        <div 
            className="flex-grow flex items-center justify-center overflow-hidden relative"
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
        >
            <img 
                src={`https://quran.ksu.edu.sa/png_big/${mushafCurrentPage}.png`} 
                alt={`صفحة المصحف رقم ${mushafCurrentPage}`} 
                className="max-w-full max-h-full object-contain select-none"
                draggable="false"
                onError={(e) => { e.currentTarget.alt = `تعذر تحميل الصفحة ${mushafCurrentPage}`; e.currentTarget.src = ""; }}
            />
        </div>


        {/* Mushaf Settings Side Panel Overlay */}
        {isMushafSettingsPanelOpen && (
            <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-[70] transition-opacity duration-300"
            onClick={() => setIsMushafSettingsPanelOpen(false)}
            aria-hidden="true"
            ></div>
        )}

        {/* Mushaf Settings Side Panel Content */}
        <div 
            className={`fixed top-0 ${document.documentElement.dir === 'rtl' ? 'right-0' : 'left-0'} 
                     h-full w-80 md:w-96 bg-card-bg shadow-2xl z-[75] 
                     transform transition-transform duration-300 ease-in-out
                     ${isMushafSettingsPanelOpen ? 'translate-x-0' : (document.documentElement.dir === 'rtl' ? 'translate-x-full' : '-translate-x-full')}
                     flex flex-col text-sm`}
            dir="rtl" 
        >
            <div className="flex justify-between items-center p-3 border-b border-border-default">
                <h3 className="text-lg font-semibold text-primary">إعدادات المصحف</h3>
                <button onClick={() => setIsMushafSettingsPanelOpen(false)} className="p-1 text-text-dark hover:text-primary rounded-md">
                    <XMarkIcon className="w-5 h-5" />
                </button>
            </div>

            <div className="flex-grow p-3 space-y-3 overflow-y-auto">
                {/* Card 1: Quick Navigation */}
                <div className="p-3 bg-background-light rounded-lg shadow-sm">
                    <h4 className="text-md font-medium text-primary mb-2">التنقل السريع</h4>
                    {/* Surah Selection */}
                    <div className="mb-2">
                        <label htmlFor="mushaf-panel-surah-select" className="block text-xs font-medium text-text-dark mb-0.5">السورة:</label>
                        <select id="mushaf-panel-surah-select" value={selectedSurahForMushaf} onChange={handleSurahSelectForMushaf} className="w-full p-1.5 border border-border-default rounded-md bg-white text-text-default text-xs focus:ring-1 focus:ring-primary-light">
                            <option value="0">اختر سورة...</option>
                            {getAllSurahNumbersForMushaf().map(num => {
                                const surahMeta = surahs.find(s => s.number === num);
                                return <option key={`panel-${num}`} value={num}>{num} - {surahMeta ? surahMeta.name : `سورة ${num}`}</option>;
                            })}
                        </select>
                    </div>
                    {/* Juz Selection */}
                    <div className="mb-2">
                        <label htmlFor="mushaf-panel-juz-select" className="block text-xs font-medium text-text-dark mb-0.5">الجزء:</label>
                        <select id="mushaf-panel-juz-select" value={selectedJuzForMushaf} onChange={handleJuzSelectForMushaf} className="w-full p-1.5 border border-border-default rounded-md bg-white text-text-default text-xs focus:ring-1 focus:ring-primary-light">
                            <option value="0">اختر جزءًا...</option>
                            {getAllJuzNumbersForMushaf().map(num => <option key={`panel-juz-${num}`} value={num}>الجزء {num}</option>)}
                        </select>
                    </div>
                    {/* Page Input */}
                    <div>
                        <label htmlFor="mushaf-panel-page-input" className="block text-xs font-medium text-text-dark mb-0.5">الصفحة:</label>
                        <div className="flex">
                            <input 
                                type="number" 
                                id="mushaf-panel-page-input"
                                value={pageInput} 
                                onChange={handlePageInputChange} 
                                min="1" max={TOTAL_MUSHAF_PAGES} 
                                className="w-full p-1.5 border border-e-0 border-border-default rounded-s-md bg-white text-text-default text-xs focus:ring-1 focus:ring-primary-light"
                            />
                            <button onClick={handleGoToPage} className="bg-primary hover:bg-primary-dark text-white px-2.5 py-1.5 rounded-e-md text-xs">اذهب</button>
                        </div>
                    </div>
                </div>

                {/* Card 2: Bookmarks */}
                <div className="p-3 bg-background-light rounded-lg shadow-sm">
                    <h4 className="text-md font-medium text-primary mb-2">العلامات المرجعية</h4>
                    <button 
                        onClick={() => toggleBookmark(mushafCurrentPage)}
                        className="w-full bg-secondary-default hover:bg-secondary-dark text-text-default px-3 py-1.5 rounded-md transition duration-150 flex items-center justify-center text-xs mb-2"
                    >
                        {mushafBookmarkedPages.includes(mushafCurrentPage) ? 
                        <BookmarkSlashIcon className="w-4 h-4 ms-1 text-accent" fill="currentColor"/> : 
                        <BookmarkIcon className="w-4 h-4 ms-1" />
                        }
                        {mushafBookmarkedPages.includes(mushafCurrentPage) ? "إزالة العلامة" : "إضافة علامة"}
                    </button>
                    <button 
                        onClick={() => { setShowBookmarksList(true); setIsMushafSettingsPanelOpen(false); }}
                        className="w-full bg-secondary-default hover:bg-secondary-dark text-text-default px-3 py-1.5 rounded-md transition duration-150 flex items-center justify-center text-xs"
                    >
                        <QueueListIcon className="w-4 h-4 ms-1" /> عرض المحفوظات
                    </button>
                </div>
            </div>
        </div>

        {/* Bookmarks List Modal (Remains the same, triggered from panel) */}
        {showBookmarksList && (
            <div className="fixed inset-0 bg-black bg-opacity-60 z-[80] flex items-center justify-center p-4" onClick={() => setShowBookmarksList(false)}>
                <div className="bg-card-bg p-4 sm:p-6 rounded-xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-y-auto" onClick={(e) => e.stopPropagation()} dir="rtl">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-xl font-semibold text-primary">العلامات المرجعية</h3>
                        <button onClick={() => setShowBookmarksList(false)} className="p-1 rounded-md hover:bg-secondary-light">
                            <XMarkIcon className="w-6 h-6 text-text-dark"/>
                        </button>
                    </div>
                    {mushafBookmarkedPages.length === 0 ? (
                        <p className="text-text-dark text-center py-4">لا توجد صفحات محفوظة.</p>
                    ) : (
                        <ul className="space-y-2">
                            {mushafBookmarkedPages.map(page => (
                                <li key={page} className="flex justify-between items-center p-3 bg-background-light rounded-lg shadow-sm">
                                    <button 
                                        onClick={() => { handleMushafPageChange(page); setShowBookmarksList(false); }}
                                        className="text-primary hover:underline text-sm"
                                    >
                                        الانتقال إلى صفحة {page}
                                    </button>
                                    <button onClick={() => toggleBookmark(page)} className="p-1 text-red-500 hover:text-red-700">
                                        <BookmarkSlashIcon className="w-5 h-5" fill="currentColor"/>
                                    </button>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            </div>
        )}
    </div>
  );
  
  const renderContent = () => {
    if (loading && viewMode === 'surahList' && surahs.length === 0 && !searchQuery) { 
        return <div className="flex justify-center items-center h-64"><Spinner /> <p className="me-2 text-text-default">جاري تحميل سور القرآن...</p></div>;
    }
    switch (viewMode) {
      case 'surahList': return renderSurahList();
      case 'ayahList': return renderAyahList();
      case 'searchResults': return renderSearchResults();
      case 'mushafView': return renderMushafView();
      default: return <p className="text-text-default">حالة عرض غير معروفة.</p>;
    }
  };

  return ( <div className={`space-y-6 ${viewMode === 'mushafView' ? 'h-full flex flex-col' : ''}`}> {renderContent()} </div> );
};
