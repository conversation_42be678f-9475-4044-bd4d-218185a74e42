{"name": "quran-<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "قرآن الهدى - تطبيق شامل لأوقات الصلاة، القرآن، الحديث، والأذكار", "private": true, "scripts": {"build": "echo \"Build step not configured. Please ensure 'www' directory is populated with final web assets.\" && exit 0", "cap:sync": "npm run build && npx cap sync", "cap:android": "npm run cap:sync && npx cap open android", "cap:ios": "npm run cap:sync && npx cap open ios"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.5.1"}, "devDependencies": {"@capacitor/cli": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/android": "^6.0.0", "@capacitor/ios": "^6.0.0", "typescript": "^5.0.0"}}