
import React, { useState, useEffect } from 'react';
import { TASBIH_PRESETS } from '../constants';
import { TasbihPreset, DhikrInSequence } from '../types';
import { HandRaisedIcon, ArrowPathIcon, CheckCircleIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon } from './icons'; // Added navigation icons

export const TasbihSection: React.FC = () => {
  const [selectedTasbihPreset, setSelectedTasbihPreset] = useState<TasbihPreset>(TASBIH_PRESETS[0]);
  const [count, setCount] = useState<number>(0);
  const [currentDhikrInSequence, setCurrentDhikrInSequence] = useState<DhikrInSequence | null>(null);
  const [currentSequenceIndex, setCurrentSequenceIndex] = useState<number>(0);
  const [isSequenceActive, setIsSequenceActive] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [customTargetInput, setCustomTargetInput] = useState<string>('0');

  useEffect(() => {
    // Initialize or reset based on selected preset
    reset(selectedTasbihPreset);
  }, [selectedTasbihPreset]);

  const reset = (preset: TasbihPreset) => {
    setCount(0);
    setMessage('');
    if (preset.sequence && preset.sequence.length > 0) {
      setIsSequenceActive(true);
      setCurrentSequenceIndex(0);
      setCurrentDhikrInSequence(preset.sequence[0]);
      setCustomTargetInput(preset.sequence[0].target.toString());
    } else {
      setIsSequenceActive(false);
      setCurrentDhikrInSequence(null);
      setCurrentSequenceIndex(0);
      setCustomTargetInput(preset.target?.toString() || '0');
    }
  };

  const increment = () => {
    const newCount = count + 1;
    setCount(newCount);
    setMessage('');

    const currentTarget = isSequenceActive && currentDhikrInSequence 
                          ? currentDhikrInSequence.target
                          : selectedTasbihPreset.target;

    if (currentTarget && newCount === currentTarget) {
      if (isSequenceActive && currentDhikrInSequence && selectedTasbihPreset.sequence) {
        setMessage(`أكملت ${currentDhikrInSequence.name} (${currentDhikrInSequence.target} مرة).`);
        if (typeof navigator !== 'undefined' && navigator.vibrate) {
          navigator.vibrate(100); 
        }
        // Move to next dhikr in sequence or complete
        const nextIndex = currentSequenceIndex + 1;
        if (nextIndex < selectedTasbihPreset.sequence.length) {
          setTimeout(() => { // Delay for user to see message
            setCurrentSequenceIndex(nextIndex);
            setCurrentDhikrInSequence(selectedTasbihPreset.sequence![nextIndex]);
            setCount(0); // Reset count for new dhikr
            setMessage(''); // Clear previous dhikr message
            setCustomTargetInput(selectedTasbihPreset.sequence![nextIndex].target.toString());
          }, 1500);
        } else {
           setTimeout(() => { // Delay for user to see message
            setMessage(`ما شاء الله! لقد أكملت ${selectedTasbihPreset.name}.`);
            setIsSequenceActive(false); // Mark sequence as complete visually
             if (typeof navigator !== 'undefined' && navigator.vibrate) {
                navigator.vibrate([100, 50, 100]); // Longer vibration for sequence completion
            }
          }, 1500);
        }
      } else { // Simple tasbih
        setMessage(`ما شاء الله! لقد أكملت ${selectedTasbihPreset.name} (${currentTarget} مرة).`);
        if (typeof navigator !== 'undefined' && navigator.vibrate) {
            navigator.vibrate(200); 
        }
      }
    }
  };

  const handlePresetChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const presetId = event.target.value;
    const preset = TASBIH_PRESETS.find(p => p.id === presetId) || TASBIH_PRESETS[0];
    setSelectedTasbihPreset(preset);
  };
  
  const handleCustomTargetChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setCustomTargetInput(value);
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 0 && selectedTasbihPreset.id === 'custom') {
        setSelectedTasbihPreset(prev => ({...prev, target: numValue }));
        setCount(0); // Reset count if target changes
        setMessage('');
    }
  };

  const currentDhikrText = isSequenceActive && currentDhikrInSequence 
                            ? currentDhikrInSequence.arabic 
                            : (selectedTasbihPreset.sequence && selectedTasbihPreset.sequence.length === 1 ? selectedTasbihPreset.sequence[0].arabic : selectedTasbihPreset.name);
  
  const currentTargetDisplay = isSequenceActive && currentDhikrInSequence 
                              ? currentDhikrInSequence.target 
                              : (selectedTasbihPreset.target || "---");

  const showNavigation = isSequenceActive && selectedTasbihPreset.sequence && selectedTasbihPreset.sequence.length > 1;

  const navigateSequence = (direction: 'next' | 'prev') => {
    if (!isSequenceActive || !selectedTasbihPreset.sequence) return;

    let newIndex = direction === 'next' ? currentSequenceIndex + 1 : currentSequenceIndex - 1;
    if (newIndex >= 0 && newIndex < selectedTasbihPreset.sequence.length) {
      setCurrentSequenceIndex(newIndex);
      setCurrentDhikrInSequence(selectedTasbihPreset.sequence[newIndex]);
      setCount(0);
      setMessage('');
      setCustomTargetInput(selectedTasbihPreset.sequence[newIndex].target.toString());
    }
  };


  return (
    <div className="space-y-6 flex flex-col items-center">
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 text-center w-full max-w-md">
        <h2 className="text-3xl font-bold text-primary mb-4 flex items-center justify-center">
         <HandRaisedIcon className="w-8 h-8 ms-3" /> المسبحة الرقمية
        </h2>
        
        <div className="mb-6">
          <label htmlFor="tasbih-select" className="block text-sm font-medium text-text-dark mb-1 text-start">
            اختر التسبيح:
          </label>
          <div className="relative">
            <select
              id="tasbih-select"
              value={selectedTasbihPreset.id}
              onChange={handlePresetChange}
              className="appearance-none w-full p-3 border border-border-default rounded-md focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light bg-background-light text-text-default ps-10 pe-3 text-start"
            >
              {TASBIH_PRESETS.map(preset => (
                <option key={preset.id} value={preset.id}>
                  {preset.name}
                </option>
              ))}
            </select>
            <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3 text-text-dark">
                <ChevronDownIcon className="w-5 h-5"/>
            </div>
          </div>
        </div>

        {selectedTasbihPreset.id === 'custom' && !isSequenceActive && (
             <div className="mb-4">
                <label htmlFor="custom-target" className="block text-sm font-medium text-text-dark mb-1 text-start">
                    أدخل العدد المستهدف:
                </label>
                <input
                    type="number"
                    id="custom-target"
                    value={customTargetInput}
                    onChange={handleCustomTargetChange}
                    min="0"
                    className="w-full p-3 border border-border-default rounded-md bg-background-light text-text-default focus:ring-2 focus:ring-primary-light"
                    placeholder="الهدف"
                />
            </div>
        )}

        {currentDhikrText && (
          <p className={`font-quran text-xl md:text-2xl text-primary my-3 min-h-[60px] flex items-center justify-center ${isSequenceActive ? 'px-10' : ''}`} dir="rtl">
            {currentDhikrText}
          </p>
        )}
        
        {showNavigation && (
            <div className="flex justify-between items-center mb-3 px-2">
                <button 
                    onClick={() => navigateSequence('prev')} 
                    disabled={currentSequenceIndex === 0}
                    className="p-2 rounded-full hover:bg-secondary-light disabled:opacity-50 active:scale-95 transform transition-transform duration-75"
                    aria-label="الذكر السابق"
                >
                    <ChevronRightIcon className="w-6 h-6 text-text-dark" />
                </button>
                <span className="text-sm text-text-dark">
                    {currentSequenceIndex + 1} / {selectedTasbihPreset.sequence!.length}
                </span>
                <button 
                    onClick={() => navigateSequence('next')} 
                    disabled={currentSequenceIndex === selectedTasbihPreset.sequence!.length - 1}
                    className="p-2 rounded-full hover:bg-secondary-light disabled:opacity-50 active:scale-95 transform transition-transform duration-75"
                    aria-label="الذكر التالي"
                >
                    <ChevronLeftIcon className="w-6 h-6 text-text-dark" />
                </button>
            </div>
        )}


        <div className="text-7xl font-mono font-bold text-text-default mb-1 p-4 border-4 border-primary rounded-full w-40 h-40 flex items-center justify-center mx-auto bg-background-light">
          {count}
        </div>
        <p className="text-sm text-text-dark mb-4">الهدف: {currentTargetDisplay}</p>


        {message && (
          <div className="my-4 p-3 bg-green-100 text-green-700 rounded-md flex items-center justify-center min-h-[50px]">
            <CheckCircleIcon className="w-5 h-5 ms-2"/> {message}
          </div>
        )}
        
        <button
          onClick={increment}
          disabled={
            message.includes("أكملت") && 
            (!isSequenceActive || (isSequenceActive && currentSequenceIndex === (selectedTasbihPreset.sequence?.length ?? 0) -1 && count === currentDhikrInSequence?.target))
          }
          className="w-full bg-primary hover:bg-primary-dark text-white font-bold py-4 px-4 rounded-lg text-2xl transition duration-150 mb-4 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light active:scale-95 transform transition-transform duration-75 disabled:opacity-60"
        >
          عد
        </button>
        <button
          onClick={() => reset(selectedTasbihPreset)}
          className="w-full bg-secondary-default hover:bg-secondary-dark text-text-default font-semibold py-3 px-4 rounded-lg transition duration-150 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-dark active:scale-95 transform transition-transform duration-75"
        >
          <ArrowPathIcon className="w-5 h-5 ms-2" /> إعادة ضبط
        </button>
      </div>
    </div>
  );
};
