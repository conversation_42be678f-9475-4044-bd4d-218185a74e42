
import React, { useState, useEffect, useCallback } from 'react';
import { fetchHadithBooks, fetchHadithsByBook } from '../services/hadithService';
import { HadithBook, HadithItem, HadithListApiResponse } from '../types';
import { Spinner } from './Spinner';
import { BackButton } from './BackButton'; // Import the new BackButton
import { ScrollIcon, InformationCircleIcon } from './icons'; 
import { HADITH_API_KEY_ERROR_SIGNATURE } from '../constants';
import { mockHadithBooksData, mockHadithsCollectionData } from '../data/hadithData';


export const HadithSection: React.FC = () => {
  const [books, setBooks] = useState<HadithBook[]>([]);
  const [selectedBook, setSelectedBook] = useState<HadithBook | null>(null);
  const [hadithsData, setHadithsData] = useState<HadithListApiResponse['hadiths'] | null>(null);
  const [loadingBooks, setLoadingBooks] = useState<boolean>(true);
  const [loadingHadiths, setLoadingHadiths] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [infoMessage, setInfoMessage] = useState<string | null>(null);
  const [usingMockData, setUsingMockData] = useState<boolean>(false);

  useEffect(() => {
    const loadBooks = async () => {
      setLoadingBooks(true);
      setError(null);
      setInfoMessage(null);
      setUsingMockData(false);
      try {
        const fetchedBooks = await fetchHadithBooks();
        setBooks(fetchedBooks);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'فشل تحميل كتب الحديث.';
        if (errorMessage.includes(HADITH_API_KEY_ERROR_SIGNATURE)) {
          setError(null); 
          setInfoMessage(`يتم عرض مجموعات أحاديث تجريبية. مفتاح API المقدم لـ hadithapi.com غير صالح أو مقيد. يرجى استخدام مفتاح API صالح في constants.ts للبيانات الحية.`);
          setBooks(mockHadithBooksData);
          setUsingMockData(true);
        } else {
          setError(errorMessage);
        }
      } finally {
        setLoadingBooks(false);
      }
    };
    loadBooks();
  }, []);

  const handleBookSelect = useCallback(async (book: HadithBook) => {
    setSelectedBook(book);
    setHadithsData(null); 
    setLoadingHadiths(true);
    setError(null); 

    const bookSlug = book.collection?.[0]?.slug || book.bookName;
    if (!bookSlug) {
      setError("معرف الكتاب (slug) غير موجود.");
      setLoadingHadiths(false);
      return;
    }
    
    if (usingMockData) {
      const mockBookData = mockHadithsCollectionData[bookSlug];
      if (mockBookData) {
        setHadithsData(mockBookData.hadiths);
        if (!infoMessage?.includes("يتم عرض مجموعات أحاديث تجريبية")) {
             setInfoMessage(`يتم عرض أحاديث تجريبية لـ ${getBookDisplayName(book)}. البيانات الحية تتطلب مفتاح API صالح.`);
        }
      } else {
        setError(`الأحاديث التجريبية غير متوفرة لـ ${getBookDisplayName(book)}.`);
        setInfoMessage(null);
      }
      setLoadingHadiths(false);
      return;
    }

    try {
      const fetchedHadiths = await fetchHadithsByBook(bookSlug, 1, 10); 
      setHadithsData(fetchedHadiths.hadiths);
      setInfoMessage(null); 
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `فشل تحميل الأحاديث لـ ${book.bookName}.`;
      if (errorMessage.includes(HADITH_API_KEY_ERROR_SIGNATURE) && !usingMockData) {
        setError(null);
        setInfoMessage(`تعذر تحميل الأحاديث الحية لـ ${getBookDisplayName(book)} بسبب مشكلة في مفتاح API. يتم عرض معلومات عامة حول مفتاح API.`);
      } else {
        setError(errorMessage);
        setInfoMessage(null);
      }
    } finally {
      setLoadingHadiths(false);
    }
  }, [usingMockData, infoMessage]);

  const handleBackToBookList = () => {
    setSelectedBook(null);
    setHadithsData(null);
    setError(null);
  };
  
  const getBookDisplayName = (book: HadithBook) => {
    // Prefer Arabic name if available and app language is Arabic (which it is now by default)
    if (book.bookNameArabic) return book.bookNameArabic;
    const englishCollection = book.collection?.find(c => c.lang === 'english');
    if (englishCollection?.name) return englishCollection.name;
    if (book.writerName && book.bookName) return `${book.bookName} - ${book.writerName}`; // Fallback if no specific display name
    return book.bookName || "مجموعة غير معروفة";
  };


  if (loadingBooks) {
    return <div className="flex justify-center items-center h-64"><Spinner /> <p className="me-2 text-text-default">جاري تحميل مجموعات الحديث...</p></div>;
  }

  return (
    <div className="space-y-6">
      {infoMessage && (
        <div className="p-4 mb-4 text-sm text-blue-700 bg-blue-100 rounded-lg flex items-start" role="alert">
          <InformationCircleIcon className="w-5 h-5 ms-2 flex-shrink-0 text-blue-500" />
          <div>
            <span className="font-medium">معلومة:</span> {infoMessage}
          </div>
        </div>
      )}

      {!selectedBook ? (
        <>
          <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
            <h2 className="text-3xl font-bold text-primary mb-2 flex items-center justify-center">
              <ScrollIcon className="w-8 h-8 ms-3" /> مجموعات الحديث
            </h2>
            <p className="text-text-dark">اختر مجموعة لقراءة الأحاديث.</p>
          </div>
          {error && !infoMessage && <p className="text-center text-error-text bg-error-background p-3 rounded-md">{error}</p>}
          
          {books.length === 0 && !loadingBooks && !infoMessage && !error && (
             <p className="text-center text-text-dark">لم يتم العثور على مجموعات حديث.</p>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {books.map(book => (
              <button
                key={book.id}
                onClick={() => handleBookSelect(book)}
                className="p-4 bg-card-bg rounded-xl shadow-md hover:shadow-lg hover:bg-gray-50 transition-all duration-300 text-start focus:outline-none focus:ring-2 focus:ring-primary-light focus:ring-opacity-60"
                aria-label={`اختر مجموعة الحديث ${getBookDisplayName(book)}`}
              >
                <h3 className="text-lg font-semibold text-primary">{getBookDisplayName(book)}</h3>
                {/* Display Arabic name if it's different and we are using mock (or actual) display name in English */}
                {book.bookNameArabic && getBookDisplayName(book) !== book.bookNameArabic && <p className="font-quran text-end text-md text-text-default" dir="rtl">{book.bookNameArabic}</p>}
                <p className="text-sm text-text-dark">المؤلف: {book.writerName || 'غير معروف'}</p>
                 {book.hadiths_count && <p className="text-xs text-text-dark mt-1">{book.hadiths_count} حديث {usingMockData && "(تقريباً)"}</p>}
              </button>
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="p-4 sm:p-6 bg-card-bg rounded-2xl shadow-lg">
            <BackButton 
                onClick={handleBackToBookList} 
                label="الرجوع إلى المجموعات" 
                className="mb-4"
            />
            <div className="text-center mb-4">
              <h2 className="text-2xl font-bold text-primary">{getBookDisplayName(selectedBook)} {usingMockData && "(بيانات تجريبية)"}</h2>
              {selectedBook.bookNameArabic && getBookDisplayName(selectedBook) !== selectedBook.bookNameArabic && <p className="font-quran text-xl text-text-default" dir="rtl">{selectedBook.bookNameArabic}</p>}
            </div>

            {loadingHadiths && <div className="flex justify-center items-center h-32"><Spinner /> <p className="me-2 text-text-default">جاري تحميل الأحاديث...</p></div>}
            {error && !infoMessage && <p className="text-center text-error-text bg-error-background p-3 rounded-md my-4">{error}</p>}
            
            {!loadingHadiths && !error && hadithsData && hadithsData.data.length > 0 && (
              <div className="space-y-4">
                {hadithsData.data.map((hadith) => (
                  <div key={hadith.id} className="p-4 border border-border-default rounded-xl bg-background-light shadow-md hover:shadow-lg transition-shadow duration-300">
                    <p className="text-sm font-semibold text-primary mb-1 text-start">
                      حديث رقم: {hadith.hadithNumber} 
                      {hadith.chapter?.chapterNumber && ` (باب: ${hadith.chapter.chapterNumber} - ${hadith.chapter.chapterArabic || hadith.chapter.chapterEnglish})`}
                    </p>
                    {hadith.englishNarrator && <p className="text-xs text-text-dark mb-2 text-start">الراوي: {hadith.englishNarrator}</p>} {/* Assuming narrator might be in English in data */}
                    
                    {hadith.hadithArabic && (
                      <p className="font-quran text-lg text-end leading-relaxed text-text-default mb-2" dir="rtl">
                        {hadith.hadithArabic}
                      </p>
                    )}
                    {hadith.hadithEnglish && ( // Show English if no Arabic, or for reference
                      <p className="text-text-default leading-relaxed text-start" dir="ltr">
                        {hadith.hadithEnglish}
                      </p>
                    )}
                    {hadith.grade && <p className="text-xs italic text-text-dark mt-2 text-start">الدرجة: {hadith.grade}</p>}
                  </div>
                ))}
                {!usingMockData && hadithsData.last_page > 1 && (
                    <p className="text-sm text-center text-text-dark mt-4">
                    عرض صفحة {hadithsData.current_page} من {hadithsData.last_page}. (يتم عرض 10 لكل صفحة حاليًا)
                    </p>
                )}
                 {usingMockData && (
                    <p className="text-sm text-center text-text-dark mt-4">
                    عرض أحاديث تجريبية.
                    </p>
                )}
              </div>
            )}
            {!loadingHadiths && !error && (!hadithsData || hadithsData.data.length === 0) && (
                 <p className="text-center text-text-dark py-4">لم يتم العثور على أحاديث أو أنها غير متوفرة في البيانات التجريبية لهذه المجموعة.</p>
            )}
          </div>
        </>
      )}
    </div>
  );
};