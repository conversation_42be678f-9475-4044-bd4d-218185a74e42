
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { fetchPrayerTimesByCoordinates, fetchPrayerTimesByCity } from '../services/prayerTimeService';
import { AlAdhanData, PrayerTimes as PrayerTimesType, HijriDate, PrayerTimesSettings, DailyReminder } from '../types';
import { DEFAULT_PRAYER_TIMES_CITY, DEFAULT_PRAYER_TIMES_COUNTRY } from '../constants';
import { dailyRemindersData } from '../data/dailyRemindersData'; // Import daily reminders
import { Spinner } from './Spinner';
import { LocationMarkerIcon, CalendarIcon, SunIcon as SunriseIcon, MoonIcon, MagnifyingGlassIcon, BellAlertIcon, ClipboardDocumentIcon, CheckCircleIcon } from './icons'; 

const PrayerTimeCard: React.FC<{ name: string; time: string; icon: React.ReactNode }> = ({ name, time, icon }) => (
  <div className="bg-card-bg p-4 rounded-xl shadow-lg hover:shadow-xl transition-shadow flex flex-col items-center text-center">
    <div className="text-primary mb-2">{icon}</div>
    <h3 className="text-lg font-semibold text-card-text">{name}</h3>
    <p className="text-2xl font-bold text-card-text">{time}</p>
  </div>
);

const DailyReminderCard: React.FC<{ reminder: DailyReminder }> = ({ reminder }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(`${reminder.textAr}\n(${reminder.sourceAr})`)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => console.error('Failed to copy text: ', err));
  };

  return (
    <div className="p-4 sm:p-5 bg-card-bg rounded-2xl shadow-lg text-center mb-6">
      <div className="flex items-center justify-center text-primary mb-2">
        <BellAlertIcon className="w-6 h-6 ms-2" />
        <h3 className="text-xl font-semibold">تذكير اليوم</h3>
      </div>
      <p className="font-quran text-lg md:text-xl text-text-default leading-loose mb-2 text-center whitespace-pre-wrap" dir="rtl">
        {reminder.textAr}
      </p>
      <p className="text-sm text-text-dark mb-3">({reminder.sourceAr})</p>
      <button
        onClick={handleCopy}
        className={`mt-2 py-1.5 px-3 rounded-md text-sm transition-colors duration-150 flex items-center justify-center mx-auto ${
          copied ? 'bg-green-500 text-white' : 'bg-secondary-default hover:bg-secondary-dark text-text-default'
        }`}
        aria-label={copied ? "تم النسخ" : "نسخ النص"}
      >
        {copied ? <CheckCircleIcon className="w-4 h-4 ms-1" /> : <ClipboardDocumentIcon className="w-4 h-4 ms-1" />}
        {copied ? 'تم النسخ!' : 'نسخ'}
      </button>
    </div>
  );
};


interface PrayerTimesSectionProps {
  settings: PrayerTimesSettings;
}

export const PrayerTimesSection: React.FC<PrayerTimesSectionProps> = ({ settings }) => {
  const [prayerTimes, setPrayerTimes] = useState<PrayerTimesType | null>(null);
  const [hijriDate, setHijriDate] = useState<HijriDate | null>(null);
  const [gregorianDate, setGregorianDate] = useState<string | null>(null);
  const [location, setLocation] = useState<string>(DEFAULT_PRAYER_TIMES_CITY);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [cityInput, setCityInput] = useState<string>(DEFAULT_PRAYER_TIMES_CITY);
  const [countryInput, setCountryInput] = useState<string>(DEFAULT_PRAYER_TIMES_COUNTRY);

  const dailyReminder: DailyReminder = useMemo(() => {
    const today = new Date();
    const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
    return dailyRemindersData[dayOfYear % dailyRemindersData.length];
  }, []);

  const formatTime = (time: string | undefined) => {
    if (!time) return 'غ/م'; 
    const [hour, minute] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hour, 10));
    date.setMinutes(parseInt(minute, 10));
    return date.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit', hour12: true });
  };
  
  const getPrayerTimes = useCallback(async (lat?: number, lon?: number) => {
    setLoading(true);
    setError(null);
    try {
      let data: AlAdhanData;
      const methodId = settings.calculationMethodId;
      const school = settings.asrJuristicMethod;
      
      if (lat && lon) {
        data = await fetchPrayerTimesByCoordinates(lat, lon, methodId, school);
        setLocation(`الموقع الحالي (${data.meta.timezone})`);
      } else {
        data = await fetchPrayerTimesByCity(cityInput, countryInput, methodId, school);
        setLocation(`${cityInput}, ${countryInput} (${data.meta.timezone})`);
      }
      setPrayerTimes(data.timings);
      setHijriDate(data.date.hijri);
      setGregorianDate(data.date.gregorian.date);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'فشل في جلب أوقات الصلاة. يرجى تجربة مدينة أخرى أو التحقق من اتصالك.');
      setPrayerTimes(null);
    } finally {
      setLoading(false);
    }
  }, [cityInput, countryInput, settings]); 

  useEffect(() => {
    if (typeof navigator.geolocation === 'undefined') {
       setError("الموقع الجغرافي غير مدعوم من متصفحك. يرجى البحث عن مدينتك يدويًا.");
       getPrayerTimes(); 
       return;
    }
    navigator.geolocation.getCurrentPosition(
      (position) => {
        getPrayerTimes(position.coords.latitude, position.coords.longitude);
      },
      (geoError) => {
        let customMessage = "فشل تحديد الموقع. يتم عرض أوقات المدينة الافتراضية/اليدوية. يمكنك محاولة البحث عن مدينتك.";
        if (geoError.code === geoError.PERMISSION_DENIED) {
          customMessage = "تم رفض إذن تحديد الموقع. يرجى تمكين الوصول إلى الموقع في إعدادات المتصفح أو البحث عن مدينتك يدويًا.";
        } else if (geoError.code === geoError.POSITION_UNAVAILABLE) {
          customMessage = "معلومات الموقع غير متوفرة. يرجى محاولة البحث عن مدينتك.";
        } else if (geoError.code === geoError.TIMEOUT) {
          customMessage = "انتهت مهلة طلب تحديد الموقع. يرجى محاولة البحث عن مدينتك.";
        }
        setError(customMessage);
        getPrayerTimes(); 
      }
    );
  }, [getPrayerTimes]);

  const handleManualSearch = (e: React.FormEvent) => {
    e.preventDefault();
    getPrayerTimes(); 
  };
  
  return (
    <div className="space-y-6">
      <DailyReminderCard reminder={dailyReminder} />

      <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
        <h2 className="text-3xl font-bold text-primary mb-2">أوقات الصلاة</h2>
        {location && <p className="text-md text-text-dark flex items-center justify-center"><LocationMarkerIcon className="w-5 h-5 ms-1 text-primary"/> {location}</p>}
        {gregorianDate && hijriDate && (
          <p className="text-md text-text-dark flex items-center justify-center mt-1">
            <CalendarIcon className="w-5 h-5 ms-1 text-primary"/> {new Date(gregorianDate.split('-').reverse().join('-')).toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })} ({hijriDate.day} {hijriDate.month.ar} {hijriDate.year} هـ)
          </p>
        )}
      </div>

      <form onSubmit={handleManualSearch} className="flex flex-col sm:flex-row gap-4 p-4 bg-card-bg rounded-2xl shadow-lg">
        <input 
          type="text" 
          value={cityInput} 
          onChange={(e) => setCityInput(e.target.value)} 
          placeholder="أدخل المدينة"
          className="flex-grow p-3 border border-border-default rounded-md focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light bg-background-light text-text-default"
        />
        <input 
          type="text" 
          value={countryInput} 
          onChange={(e) => setCountryInput(e.target.value)} 
          placeholder="أدخل الدولة (اختياري)"
          className="flex-grow p-3 border border-border-default rounded-md focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light bg-background-light text-text-default"
        />
        <button type="submit" className="bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-md transition duration-150 flex items-center justify-center active:scale-95 transform transition-transform duration-75">
          <MagnifyingGlassIcon className="w-5 h-5 ms-2" /> بحث
        </button>
      </form>
      
      {loading && <div className="flex justify-center items-center h-40"><Spinner /> <p className="me-2 text-text-default">جاري تحميل أوقات الصلاة...</p></div>}
      {error && <p className="text-center text-error-text bg-error-background p-3 rounded-md">{error}</p>}

      {prayerTimes ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
          <PrayerTimeCard name="الفجر" time={formatTime(prayerTimes.Fajr)} icon={<SunriseIcon className="w-8 h-8 text-text-dark" />} />
          <PrayerTimeCard name="الظهر" time={formatTime(prayerTimes.Dhuhr)} icon={<SunriseIcon className="w-8 h-8 text-primary" />} />
          <PrayerTimeCard name="العصر" time={formatTime(prayerTimes.Asr)} icon={<SunriseIcon className="w-8 h-8 text-text-dark" />} />
          <PrayerTimeCard name="المغرب" time={formatTime(prayerTimes.Maghrib)} icon={<SunriseIcon className="w-8 h-8 text-primary" />} />
          <PrayerTimeCard name="العشاء" time={formatTime(prayerTimes.Isha)} icon={<MoonIcon className="w-8 h-8 text-primary" />} />
        </div>
      ) : (
        !loading && <p className="text-center text-text-dark">لا توجد أوقات صلاة لعرضها. حاول البحث عن مدينة.</p>
      )}
    </div>
  );
};
