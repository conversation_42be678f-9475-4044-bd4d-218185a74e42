import React from 'react';
import { asmaulHusnaData } from '../data/asmaulHusnaData';
import { AllahsN<PERSON> } from '../types';
import { HeartIcon } from './icons'; // Using HeartIcon for the section header

export const AsmaulHusnaSection: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
        <h2 className="text-3xl font-bold text-primary mb-2 flex items-center justify-center">
          <HeartIcon className="w-8 h-8 ms-3 text-primary" /> أسماء الله الحسنى
        </h2>
        <p className="text-text-dark">تعرف على أسماء الله الحسنى ومعانيها.</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {asmaulHusnaData.map((nameItem: AllahsName) => (
          <div 
            key={nameItem.id} 
            className="p-5 bg-card-bg rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center text-center"
            role="listitem"
            aria-labelledby={`name-${nameItem.id}`}
            aria-describedby={`meaning-${nameItem.id}`}
          >
            <span className="text-xs px-2 py-0.5 bg-primary-light text-primary-dark rounded-full mb-2 self-start">
              {nameItem.id}
            </span>
            <h3 id={`name-${nameItem.id}`} className="text-2xl font-quran font-semibold text-primary mb-1">
              {nameItem.nameAr}
            </h3>
            <p className="text-sm text-text-dark mb-2">({nameItem.nameTransliteration})</p>
            <p id={`meaning-${nameItem.id}`} className="text-sm text-card-text leading-relaxed">
              {nameItem.meaningAr}
            </p>
            {/* Optional: English meaning if available and needed
            {nameItem.meaningEn && (
              <p className="text-xs text-gray-500 mt-1" dir="ltr">{nameItem.meaningEn}</p>
            )}
            */}
          </div>
        ))}
      </div>
       <div className="mt-6 p-4 text-sm text-text-dark bg-secondary-light border-s-4 border-secondary-dark rounded-md" role="alert">
          <p>
            المصدر الأساسي للمعاني هو شرح أسماء الله الحسنى للشيخ عبد الرحمن بن ناصر السعدي رحمه الله، مع بعض التصرف لتبسيط العرض.
          </p>
        </div>
    </div>
  );
};
