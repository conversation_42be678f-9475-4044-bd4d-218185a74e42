import React from 'react';
import { ChevronRightIcon } from './icons'; // Assuming RTL, so right chevron means "back"

interface BackButtonProps {
  onClick: () => void;
  label?: string;
  className?: string;
}

export const BackButton: React.FC<BackButtonProps> = ({ 
  onClick, 
  label = "الرجوع", 
  className = "" 
}) => {
  return (
    <button
      onClick={onClick}
      className={`flex items-center text-sm text-primary hover:text-primary-dark focus:outline-none active:scale-95 transform transition-transform duration-75 p-2 rounded-md hover:bg-primary-light hover:bg-opacity-20 ${className}`}
      aria-label={label}
    >
      <ChevronRightIcon className="w-5 h-5 ms-1" /> 
      {label}
    </button>
  );
};
