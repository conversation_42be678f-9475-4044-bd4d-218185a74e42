import { AppSettings, PrayerCalculationMethod, AsrJuristicOption, TasbihPreset } from "./types";

export const ALADHAN_API_BASE_URL = 'https://api.aladhan.com/v1';
export const QURAN_API_BASE_URL = 'https://api.alquran.cloud/v1';
export const HADITH_API_BASE_URL = 'https://www.hadithapi.com/api';

export const HADITH_API_KEY = 'DEMO_KEY'; 
export const HADITH_API_KEY_ERROR_SIGNATURE = "API Key is invalid or missing";


export const DEFAULT_PRAYER_TIMES_CITY = 'Makkah';
export const DEFAULT_PRAYER_TIMES_COUNTRY = 'Saudi Arabia';
export const DEFAULT_PRAYER_CALCULATION_METHOD_ID = 4; 
export const DEFAULT_ASR_JURISTIC_METHOD = 'standard';


export const DEFAULT_QURAN_AYAH_REF = '2:255'; 
export const QURAN_ARABIC_EDITION = 'quran-uthmani';
export const QURAN_ENGLISH_TRANSLATION_EDITION = 'en.sahih'; 
export const QURAN_AUDIO_EDITION = 'ar.alafasy'; 
export const QURAN_TAFSIR_EDITION = 'en.ibnkatheer'; 

export const AVAILABLE_TRANSLATIONS = [
  { identifier: 'en.sahih', name: 'الإنجليزية (صحيح انترناشونال)', language: 'en' },
  { identifier: 'es.cortes', name: 'الإسبانية (خوليو كورتيس)', language: 'es' },
  { identifier: 'fr.hamidullah', name: 'الفرنسية (محمد حميد الله)', language: 'fr' },
  { identifier: 'ru.kuliev', name: 'الروسية (إلمير كولييف)', language: 'ru' },
  { identifier: 'de.aburida', name: 'الألمانية (أبو رضا محمد بن أحمد بن رسول)', language: 'de' },
  { identifier: 'tr.diyanet', name: 'التركية (رئاسة الشؤون الدينية)', language: 'tr' },
  { identifier: 'id.indonesian', name: 'الإندونيسية (وزارة الشؤون الدينية الإندونيسية)', language: 'id' }
];

export const TASBIH_PRESETS: TasbihPreset[] = [
  { id: 'subhanallah_33', name: 'سبحان الله (33 مرة)', target: 33, sequence: [{ id: 'sp1_subhan', name: 'سبحان الله', arabic: 'سبحان الله', target: 33 }] },
  { id: 'alhamdulillah_33', name: 'الحمد لله (33 مرة)', target: 33, sequence: [{ id: 'sp1_alhamd', name: 'الحمد لله', arabic: 'الحمد لله', target: 33 }] },
  { id: 'allahuakbar_33', name: 'الله أكبر (33 مرة)', target: 33, sequence: [{ id: 'sp1_akbar', name: 'الله أكبر', arabic: 'الله أكبر', target: 33 }] },
  { id: 'lailahaillallah_100', name: 'لا إله إلا الله (100 مرة)', target: 100, sequence: [{ id: 'sp1_lailaha', name: 'لا إله إلا الله', arabic: 'لا إله إلا الله', target: 100 }] },
  {
    id: 'post_prayer_adhkar',
    name: 'أذكار ما بعد الصلاة',
    sequence: [
      { id: 'ppa_subhanallah', name: 'التسبيح', arabic: 'سبحان الله', target: 33 },
      { id: 'ppa_alhamdulillah', name: 'التحميد', arabic: 'الحمد لله', target: 33 },
      { id: 'ppa_allahuakbar', name: 'التكبير', arabic: 'الله أكبر', target: 33 },
      { id: 'ppa_tahlil', name: 'التهليل (إكمال المئة)', arabic: 'لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير', target: 1 },
    ],
  },
  { id: 'custom', name: 'مخصص (أدخل العدد)', target: 0 } // Represents a custom target if no sequence
];


export const HADITH_BOOK_SLUGS = {
  BUKHARI: 'sahih-bukhari',
  MUSLIM: 'sahih-muslim',
  ABU_DAWUD: 'sunan-abu-dawud',
  TIRMIDHI: 'jami-at-tirmidhi',
  NASAI: 'sunan-an-nasai',
  IBN_MAJAH: 'sunan-ibn-majah'
};

// --- Settings Constants ---
export const DEFAULT_APP_SETTINGS: AppSettings = {
  prayerTimes: {
    calculationMethodId: DEFAULT_PRAYER_CALCULATION_METHOD_ID,
    asrJuristicMethod: DEFAULT_ASR_JURISTIC_METHOD,
    fajrOffset: 0,
    dhuhrOffset: 0,
    asrOffset: 0,
    maghribOffset: 0,
    ishaOffset: 0
  },
  quran: {
    defaultTranslation: QURAN_ENGLISH_TRANSLATION_EDITION, 
    arabicFontSize: 'medium',
    translationFontSize: 'medium',
    mushafViewSettings: {
        currentPage: 1,
        bookmarkedPages: []
    }
  },
  general: {
    appLanguage: 'ar',
    themeMode: 'light',
  }
};

export const PRAYER_CALCULATION_METHODS: PrayerCalculationMethod[] = [
  { id: 2, name: 'الجمعية الإسلامية لأمريكا الشمالية (ISNA)' },
  { id: 3, name: 'رابطة العالم الإسلامي (MWL)' },
  { id: 4, name: 'جامعة أم القرى، مكة المكرمة' },
  { id: 5, name: 'الهيئة المصرية العامة للمساحة' },
  { id: 1, name: 'جامعة العلوم الإسلامية، كراتشي' },
  { id: 12, name: 'رئاسة الشؤون الدينية التركية'}
];

export const ASR_JURISTIC_OPTIONS: AsrJuristicOption[] = [
  { id: 'standard', name: 'شافعي، مالكي، حنبلي (قياسي)' },
  { id: 'hanafi', name: 'حنفي' }
];

export const FONT_SIZE_OPTIONS = [
    {id: 'small', name: 'صغير'},
    {id: 'medium', name: 'متوسط'},
    {id: 'large', name: 'كبير'}
];

export const LANGUAGE_OPTIONS = [
  {id: 'ar', name: 'العربية'},
  {id: 'en', name: 'English'}
];

export const THEME_OPTIONS = [
  {id: 'light', name: 'فاتح'},
  {id: 'dark', name: 'داكن'}
];

export const TOTAL_MUSHAF_PAGES = 604;