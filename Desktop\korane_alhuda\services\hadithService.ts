
import { HADITH_API_BASE_URL, HADITH_API_KEY, HADITH_API_KEY_ERROR_SIGNATURE } from '../constants';
import { HadithBookListApiResponse, HadithListApiResponse, HadithBook } from '../types';

export const fetchHadithBooks = async (): Promise<HadithBook[]> => {
  const response = await fetch(`${HADITH_API_BASE_URL}/books?apiKey=${HADITH_API_KEY}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    if (response.status === 401) {
      throw new Error(`${HADITH_API_KEY_ERROR_SIGNATURE}. Please obtain a valid API key from hadithapi.com. (Status: ${response.status})`);
    }
    throw new Error(`Failed to fetch Hadith books: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
  }
  
  const apiResponse: HadithBookListApiResponse | HadithBook[] = await response.json(); 
  
  if (Array.isArray(apiResponse)) {
    return apiResponse as HadithBook[];
  } else if (apiResponse && 'books' in apiResponse && Array.isArray(apiResponse.books)) {
     return apiResponse.books;
  } else {
    console.error("Unexpected API response structure for Hadith books:", apiResponse);
    throw new Error('Unexpected API response structure for Hadith books.');
  }
};

export const fetchHadithsByBook = async (bookSlug: string, page: number = 1, limit: number = 20): Promise<HadithListApiResponse> => {
  const response = await fetch(`${HADITH_API_BASE_URL}/hadiths/${bookSlug}?apiKey=${HADITH_API_KEY}&limit=${limit}&page=${page}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    if (response.status === 401) {
      throw new Error(`${HADITH_API_KEY_ERROR_SIGNATURE} for book ${bookSlug}. Please obtain a valid API key from hadithapi.com. (Status: ${response.status})`);
    }
    throw new Error(`Failed to fetch Hadiths for ${bookSlug}: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
  }
  const apiResponse: HadithListApiResponse = await response.json();
  if (!apiResponse.hadiths || !Array.isArray(apiResponse.hadiths.data)) {
     throw new Error(`Invalid data structure for Hadiths from ${bookSlug}.`);
  }
  return apiResponse;
};