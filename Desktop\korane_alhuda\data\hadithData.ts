
import { HadithBook, HadithListApiResponse, HadithItem } from '../types';

export const mockHadithBooksData: HadithBook[] = [
  { 
    id: 1, 
    bookName: 'sahih-bukhari', 
    writerName: 'الإمام البخاري', 
    writerDeath: '256 هـ',
    aboutWriter: 'محمد بن إسماعيل البخاري، المعروف بالإمام البخاري، محدث من القرن التاسع، يعتبر من أهم علماء الحديث النبوي في الإسلام السني.',
    bookNameArabic: 'صحيح البخاري', 
    collection: [{ name: 'صحيح البخاري (عينة)', slug: 'sahih-bukhari', lang: 'ar' }], 
    hadiths_count: 7563,
    chapters_count: 97 
  },
  { 
    id: 2, 
    bookName: 'sahih-muslim', 
    writerName: 'الإمام مسلم', 
    writerDeath: '261 هـ',
    aboutWriter: 'مسلم بن الحجاج، المعروف بالإمام مسلم، عالم إسلامي من القرن التاسع، اشتهر كمحدث. مجموعته الحديثية، صحيح مسلم، من أكثر كتب الحديث المعتبرة.',
    bookNameArabic: 'صحيح مسلم', 
    collection: [{ name: 'صحيح مسلم (عينة)', slug: 'sahih-muslim', lang: 'ar' }], 
    hadiths_count: 7500, // Approx
    chapters_count: 57 // Approx
  },
   { 
    id: 3, 
    bookName: 'sunan-abu-dawud', 
    writerName: 'الإمام أبو داود', 
    writerDeath: '275 هـ',
    aboutWriter: 'أبو داود سليمان بن الأشعث الأزدي السجستاني، المعروف بأبي داود، عالم حديث من القرن التاسع، اشتهر بمجموعته سنن أبي داود.',
    bookNameArabic: 'سنن أبي داود', 
    collection: [{ name: 'سنن أبي داود (عينة)', slug: 'sunan-abu-dawud', lang: 'ar' }], 
    hadiths_count: 5274,
    chapters_count: 43
  },
];

const sampleBukhariHadiths: HadithItem[] = [
  { 
    id: 1001, 
    hadithNumber: '1', 
    bookId: 1, 
    book: { id: 1, bookName: 'صحيح البخاري (عينة)', writerName: 'الإمام البخاري' }, 
    chapterId: 1, 
    chapter: { id: 1, chapterNumber: '1', chapterArabic: 'كتاب بدء الوحى', chapterEnglish: 'Revelation' }, 
    englishNarrator: "عمر بن الخطاب", // Narrator name might remain in its common form or be transliterated
    hadithArabic: "حَدَّثَنَا الْحُمَيْدِيُّ عَبْدُ اللَّهِ بْنُ الزُّبَيْرِ، قَالَ حَدَّثَنَا سُفْيَانُ، قَالَ حَدَّثَنَا يَحْيَى بْنُ سَعِيدٍ الأَنْصَارِيُّ، قَالَ أَخْبَرَنِي مُحَمَّدُ بْنُ إِبْرَاهِيمَ التَّيْمِيُّ، أَنَّهُ سَمِعَ عَلْقَمَةَ بْنَ وَقَّاصٍ اللَّيْثِيَّ، يَقُولُ سَمِعْتُ عُمَرَ بْنَ الْخَطَّابِ ـ رضى الله عنه ـ عَلَى الْمِنْبَرِ قَالَ سَمِعْتُ رَسُولَ اللَّهِ صلى الله عليه وسلم يَقُولُ ‏ \"‏ إِنَّمَا الأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى، فَمَنْ كَانَتْ هِجْرَتُهُ إِلَى دُنْيَا يُصِيبُهَا أَوْ إِلَى امْرَأَةٍ يَنْكِحُهَا فَهِجْرَتُهُ إِلَى مَا هَاجَرَ إِلَيْهِ ‏\"‏‏.‏", 
    hadithEnglish: "Narrated 'Umar bin Al-Khattab:\nI heard Allah's Messenger (ﷺ) saying, \"The reward of deeds depends upon the intentions and every person will get the reward according to what he has intended. So whoever emigrated for worldly benefits or for a woman to marry, his emigration was for what he emigrated for.\"",
    grade: "صحيح"
  },
  { 
    id: 1002, 
    hadithNumber: '2', 
    bookId: 1, 
    book: { id: 1, bookName: 'صحيح البخاري (عينة)', writerName: 'الإمام البخاري' }, 
    chapterId: 1, 
    chapter: { id: 1, chapterNumber: '1', chapterArabic: 'كتاب بدء الوحى', chapterEnglish: 'Revelation' }, 
    englishNarrator: "عائشة (أم المؤمنين)",
    hadithArabic: "حَدَّثَنَا عَبْدُ اللَّهِ بْنُ يُوسُفَ، قَالَ أَخْبَرَنَا مَالِكٌ، عَنْ هِشَامِ بْنِ عُرْوَةَ، عَنْ أَبِيهِ، عَنْ عَائِشَةَ ـ أُمِّ الْمُؤْمِنِينَ ـ رضى الله عنها ـ أَنَّ الْحَارِثَ بْنَ هِشَامٍ ـ رضى الله عنه ـ سَأَلَ رَسُولَ اللَّهِ صلى الله عليه وسلم فَقَالَ يَا رَسُولَ اللَّهِ كَيْفَ يَأْتِيكَ الْوَحْىُ فَقَالَ رَسُولُ اللَّهِ صلى الله عليه وسلم ‏\"‏ أَحْيَانًا يَأْتِينِي مِثْلَ صَلْصَلَةِ الْجَرَسِ ـ وَهُوَ أَشَدُّهُ عَلَىَّ ـ فَيُفْصَمُ عَنِّي وَقَدْ وَعَيْتُ عَنْهُ مَا قَالَ، وَأَحْيَانًا يَتَمَثَّلُ لِيَ الْمَلَكُ رَجُلاً فَيُكَلِّمُنِي فَأَعِي مَا يَقُولُ ‏\"‏‏.‏ قَالَتْ عَائِشَةُ رضى الله عنها وَلَقَدْ رَأَيْتُهُ يَنْزِلُ عَلَيْهِ الْوَحْىُ فِي الْيَوْمِ الشَّدِيدِ الْبَرْدِ، فَيَفْصِمُ عَنْهُ وَإِنَّ جَبِينَهُ لَيَتَفَصَّدُ عَرَقًا‏.‏", 
    hadithEnglish: "Narrated 'Aisha: (the mother of the faithful believers) Al-Harith bin Hisham asked Allah's Messenger (ﷺ) \"O Allah's Messenger (ﷺ)! How is the Divine Inspiration revealed to you?\" Allah's Messenger (ﷺ) replied, \"Sometimes it is 'revealed' like the ringing of a bell, this form of Inspiration is the hardest of all and then this state passes ' off after I have grasped what is inspired. Sometimes the Angel comes in the form of a man and talks to me and I grasp whatever he says.\" 'Aisha added: Verdantly I saw the Prophet (ﷺ) being inspired Divinely on a very cold day and noticed the sweat dropping from his forehead (as the Inspiration was over).",
    grade: "صحيح"
  },
];

export const mockHadithsCollectionData: { [bookSlug: string]: HadithListApiResponse } = {
  'sahih-bukhari': {
    hadiths: {
      data: sampleBukhariHadiths,
      total: sampleBukhariHadiths.length,
      per_page: 10,
      current_page: 1,
      last_page: 1,
    },
    book: mockHadithBooksData.find(b => b.bookName === 'sahih-bukhari'),
  },
  'sahih-muslim': {
     hadiths: {
      data: [
        { id: 2001, hadithNumber: '1', bookId:2, book: {id:2, bookName: 'صحيح مسلم (عينة)'}, chapterId:1, chapter: {id:1, chapterNumber:'1', chapterArabic:'كتاب الإيمان', chapterEnglish:'Faith'}, hadithArabic: "حَدَّثَنَا أَبُو بَكْرِ بْنُ أَبِي شَيْبَةَ...", hadithEnglish: "It is narrated on the authority of Amirul Mu'minin Abu Hafs `Umar b. al-Khattab, who said: I heard the Messenger of Allah (ﷺ) say: Actions are (judged) by motives (niyyah), so each man will have what he intended...", englishNarrator: "عمر بن الخطاب", grade: "صحيح"}
      ],
      total: 1,
      per_page: 10,
      current_page: 1,
      last_page: 1,
    },
    book: mockHadithBooksData.find(b => b.bookName === 'sahih-muslim'),
  },
  'sunan-abu-dawud': {
     hadiths: {
      data: [
        { id: 3001, hadithNumber: '1', bookId:3, book: {id:3, bookName: 'سنن أبي داود (عينة)'}, chapterId:1, chapter: {id:1, chapterNumber:'1', chapterArabic:'كتاب الطهارة', chapterEnglish:'Purification (Kitab Al-Taharah)'}, hadithArabic: "حَدَّثَنَا عَبْدُ اللَّهِ بْنُ مَسْلَمَةَ بْنِ قَعْنَبٍ الْقَعْنَبِيُّ...", hadithEnglish: "Narrated AbuMusa al-Ash'ari: The Prophet (ﷺ) said: The key to prayer is purification; its beginning is takbir (saying AllahuAkbar) and its end is taslim (saying As-salamu alaykum wa Rahmatullah).", englishNarrator: "أبو موسى الأشعري", grade: "صحيح"}
      ],
      total: 1,
      per_page: 10,
      current_page: 1,
      last_page: 1,
    },
    book: mockHadithBooksData.find(b => b.bookName === 'sunan-abu-dawud'),
  }
};