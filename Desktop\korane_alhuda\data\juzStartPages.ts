// data/juzStartPages.ts
// Maps Juz' number to its starting page number in a 604-page Mushaf
export const juzStartPages: Record<number, number> = {
  1: 1,
  2: 22,
  3: 42,
  4: 62,
  5: 82,
  6: 102,
  7: 121, // Note: <PERSON><PERSON> 7 starts on page 121 in many KSU prints due to Rubu Hizb markers, not exactly page 122.
  8: 142,
  9: 162,
  10: 182,
  11: 201,
  12: 222,
  13: 242,
  14: 262,
  15: 282,
  16: 302,
  17: 322,
  18: 342,
  19: 362,
  20: 382,
  21: 402,
  22: 422,
  23: 442,
  24: 462,
  25: 482,
  26: 502,
  27: 522,
  28: 542,
  29: 562,
  30: 582,
};

// Helper function to get all juz numbers for dropdown
export const getAllJuzNumbersForMushaf = (): number[] => {
  return Array.from({ length: 30 }, (_, i) => i + 1);
};
