
import React, { useState } from 'react';
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { Spinner } from './Spinner';
import { SparklesIcon, LightBulbIcon, InformationCircleIcon } from './icons'; // Using SparklesIcon as main

const API_KEY_STORAGE_KEY = 'geminiApiKey';

export const AiAdhkarGeneratorSection: React.FC = () => {
  const [userInput, setUserInput] = useState<string>('');
  const [generatedDhikr, setGeneratedDhikr] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateDhikr = async () => {
    if (!userInput.trim()) {
      setError('يرجى إدخال موضوع للذكر (مثال: أذكار الصباح، أذكار عند الخروج من المنزل).');
      return;
    }

    const apiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
    if (!apiKey) {
      setError("مفتاح Gemini API غير متوفر. يرجى إضافته من قسم 'الإعدادات' لتتمكن من استخدام هذه الميزة.");
      console.error("Gemini API key is missing from localStorage.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setGeneratedDhikr('');

    try {
      const ai = new GoogleGenAI({ apiKey: apiKey });
      const model = 'gemini-2.5-flash-preview-04-17';
      
      const systemInstruction = "أنت مساعد ذكاء اصطناعي متخصص في إنشاء الأذكار الإسلامية. مهمتك هي صياغة ذكر باللغة العربية الفصحى، يكون ذا صلة بطلب المستخدم، ومستوحى من روح ومبادئ القرآن الكريم والسنة النبوية. يجب أن يكون الذكر أصيلاً ومناسبًا، ويمكن أن يتضمن فضله أو مرجعه إذا كان ذلك معروفًا ومناسبًا للسياق. لا تقم بتضمين أي مقدمات أو خواتيم غير ضرورية، فقط نص الذكر مباشرة.";
      const prompt = `أنشئ ذكرًا إسلاميًا لـ: ${userInput}`;

      const response: GenerateContentResponse = await ai.models.generateContent({
        model: model,
        contents: prompt,
        config: {
          systemInstruction: systemInstruction,
          temperature: 0.6, // Slightly less creative for Adhkar to stick to known forms if possible
        }
      });
      
      const text = response.text;
      if (text) {
        setGeneratedDhikr(text);
      } else {
        setError('لم يتمكن الذكاء الاصطناعي من إنشاء ذكر لهذه المدخلات. حاول مرة أخرى أو قم بتغيير الموضوع.');
      }
    } catch (e) {
      console.error("Error generating dhikr:", e);
      let errorMessage = 'حدث خطأ أثناء إنشاء الذكر. يرجى المحاولة مرة أخرى.';
      if (e instanceof Error) {
          if (e.message.includes('API key not valid') || e.message.includes('permission denied') || e.message.includes('API_KEY_INVALID')) {
              errorMessage = 'مفتاح API المستخدم غير صالح أو هناك مشكلة في صلاحيات الوصول. يرجى مراجعة المفتاح في قسم "الإعدادات" أو التأكد من صلاحيته.';
          } else if (e.message.includes('quota')) {
              errorMessage = 'تم تجاوز الحصة المسموح بها لـ API. يرجى المحاولة لاحقًا.';
          } else if (e.message.toLowerCase().includes('candidate was blocked')) {
              errorMessage = 'تم حظر الرد من قبل النموذج بسبب سياسات المحتوى. حاول تغيير صياغة طلبك.';
          }
      }
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
        <h2 className="text-3xl font-bold text-primary mb-2 flex items-center justify-center">
          <SparklesIcon className="w-8 h-8 ms-3" /> مولد الأذكار بالذكاء الاصطناعي
        </h2>
        <p className="text-text-dark">اطلب من الذكاء الاصطناعي إنشاء ذكر لك بناءً على مناسبة أو وقت معين، مستلهمًا من الكتاب والسنة.</p>
      </div>

      <div className="p-6 bg-card-bg rounded-2xl shadow-lg">
        <div className="mb-4">
          <label htmlFor="dhikr-topic" className="block text-sm font-medium text-text-dark mb-1 text-start">
            موضوع الذكر (مثال: أذكار الصباح، أذكار المساء، أذكار النوم، أذكار دخول المسجد):
          </label>
          <textarea
            id="dhikr-topic"
            rows={3}
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder="اكتب هنا الموضوع الذي تريد إنشاء ذكر له..."
            className="w-full p-3 border border-border-default rounded-md focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light bg-background-light text-text-default"
            aria-label="موضوع الذكر"
          />
        </div>

        <button
          onClick={handleGenerateDhikr}
          disabled={isLoading}
          className="w-full bg-primary hover:bg-primary-dark text-white font-bold py-3 px-4 rounded-md transition duration-150 flex items-center justify-center disabled:opacity-70 active:scale-95 transform transition-transform duration-75"
        >
          {isLoading ? (
            <>
              <Spinner />
              <span className="me-2">جاري إنشاء الذكر...</span>
            </>
          ) : (
            <>
              <LightBulbIcon className="w-5 h-5 ms-2" /> إنشاء ذكر
            </>
          )}
        </button>

        {error && (
          <p className="mt-4 text-center text-error-text bg-error-background p-3 rounded-md">{error}</p>
        )}
      </div>

      {generatedDhikr && !isLoading && (
        <div className="p-6 bg-card-bg rounded-2xl shadow-xl animate-fadeIn">
          <h3 className="text-xl font-semibold text-primary mb-3 text-start">الذكر المُنشأ:</h3>
          <div className="p-4 bg-background-light border border-primary-light rounded-lg">
            <p className="font-quran text-lg md:text-xl text-text-default leading-loose text-center whitespace-pre-wrap" dir="rtl">
              {generatedDhikr}
            </p>
          </div>
        </div>
      )}
      
      <div className="mt-6 p-4 text-sm text-text-dark bg-secondary-light border-s-4 border-secondary-dark rounded-md flex items-start gap-3" role="alert">
        <InformationCircleIcon className="w-8 h-8 text-secondary-dark flex-shrink-0" />
        <div>
          <h4 className="font-semibold text-text-default">تنويه هام:</h4>
          <p>
            هذه الأداة تستخدم الذكاء الاصطناعي لإنشاء أذكار بناءً على طلبك، وهي للمساعدة والإلهام.
            الأذكار المأثورة من القرآن الكريم والسنة النبوية هي الأساس والأفضل دائمًا.
            يُرجى مراجعة أهل العلم للتأكد من صحة ومناسبة الأذكار المُنشأة، خاصةً وأن بعض الأذكار لها صيغ وأعداد محددة.
          </p>
        </div>
      </div>
    </div>
  );
};
