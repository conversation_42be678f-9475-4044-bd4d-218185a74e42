import { QURAN_API_BASE_URL, QURAN_AUDIO_EDITION } from '../constants';
import { 
  QuranVerseApiResponse, 
  QuranAyahData, 
  SurahListApiResponse, 
  SurahMetadata,
  FullSurahApiResponse,
  SurahFullEditionData,
  AyahTafsirApiResponse,
  AyahTafsirData,
  QuranSearchApiResponse
} from '../types';

export const fetchSingleQuranVerse = async (ayahReference: string, editionIdentifier: string): Promise<QuranAyahData> => {
  const response = await fetch(`${QURAN_API_BASE_URL}/ayah/${ayahReference}/${editionIdentifier}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to fetch Quran verse (${ayahReference}, ${editionIdentifier}): ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const apiResponse: QuranVerseApiResponse = await response.json();
  if (apiResponse.code !== 200 || apiResponse.status !== "OK") {
    throw new Error(`API Error: ${apiResponse.status} for ${ayahReference}, ${editionIdentifier}`);
  }
  return apiResponse.data;
};

export const fetchAllSurahsMetadata = async (): Promise<SurahMetadata[]> => {
  const response = await fetch(`${QURAN_API_BASE_URL}/surah`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to fetch Surah list: ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const apiResponse: SurahListApiResponse = await response.json();
  if (apiResponse.code !== 200 || apiResponse.status !== "OK") {
    throw new Error(`API Error fetching Surah list: ${apiResponse.status}`);
  }
  return apiResponse.data;
};

export const fetchFullSurahWithEditions = async (
  surahNumber: number, 
  arabicEdition: string,
  translationEdition: string, 
  includeAudio: boolean = true // if audio edition is fixed, it could be hardcoded
): Promise<SurahFullEditionData[]> => {
  
  const editionsToFetch = [arabicEdition, translationEdition];
  if (includeAudio && QURAN_AUDIO_EDITION) {
    // Ensure audio edition is not duplicated if it's somehow same as translation/arabic
    if (!editionsToFetch.includes(QURAN_AUDIO_EDITION)) {
        editionsToFetch.push(QURAN_AUDIO_EDITION);
    }
  }

  if (editionsToFetch.length === 0) {
    throw new Error("At least one edition must be specified.");
  }

  const editionsParam = editionsToFetch.join(',');
  const response = await fetch(`${QURAN_API_BASE_URL}/surah/${surahNumber}/editions/${editionsParam}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to fetch Surah ${surahNumber} with editions ${editionsParam}: ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const apiResponse: FullSurahApiResponse = await response.json();
  if (apiResponse.code !== 200 || apiResponse.status !== "OK") {
     if (typeof apiResponse.data === 'string') {
        throw new Error(`API Error fetching Surah ${surahNumber}: ${apiResponse.data}`);
    }
    throw new Error(`API Error fetching Surah ${surahNumber}: ${apiResponse.status}`);
  }
  if (!Array.isArray(apiResponse.data)) {
    throw new Error(`Invalid data structure received for Surah ${surahNumber}. Expected an array of editions.`);
  }
  return apiResponse.data;
};

export const fetchAyahTafsir = async (ayahNumberInQuran: number, tafsirEditionIdentifier: string): Promise<AyahTafsirData> => {
  const response = await fetch(`${QURAN_API_BASE_URL}/ayah/${ayahNumberInQuran}/${tafsirEditionIdentifier}`);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to fetch Tafsir for Ayah ${ayahNumberInQuran} (${tafsirEditionIdentifier}): ${response.status} ${response.statusText}. ${errorData.data || ''}`);
  }
  const apiResponse: AyahTafsirApiResponse = await response.json();
  if (apiResponse.code !== 200 || apiResponse.status !== "OK") {
    throw new Error(`API Error for Tafsir Ayah ${ayahNumberInQuran} (${tafsirEditionIdentifier}): ${apiResponse.status}`);
  }
  return apiResponse.data;
};

export const searchQuran = async (query: string, searchInEdition: string, scope: string = 'all'): Promise<QuranSearchApiResponse> => {
  const encodedQuery = encodeURIComponent(query);
  // The API expects /search/{query}/{scope}/{edition}
  // If scope is 'all', it might be '/search/{query}/all/{edition}' or simply '/search/{query}/{edition}' if 'all' is default.
  // The documentation at alquran.cloud/api shows /search/{keyword}/{surah|all}/{edition?}
  // Let's assume {edition} is mandatory if specified.
  const url = `${QURAN_API_BASE_URL}/search/${encodedQuery}/${scope}/${searchInEdition}`;
  
  const response = await fetch(url);
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to search Quran for "${query}" in ${searchInEdition}: ${response.status} ${response.statusText}. ${errorData.data || errorData.error || ''}`);
  }
  const apiResponse: QuranSearchApiResponse = await response.json();
   if (apiResponse.code !== 200 || apiResponse.status.toUpperCase() !== "OK") { // API status can be "Ok"
    throw new Error(`API Error during Quran search for "${query}": ${apiResponse.status}`);
  }
  if (!apiResponse.data || !apiResponse.data.matches) {
     throw new Error(`Invalid data structure received for Quran search results for "${query}".`);
  }
  return apiResponse;
};