
import React, { useState, useEffect } from 'react';
import { AppSettings, PrayerTimesSettings, QuranSettings, GeneralSettings } from '../types';
import { 
  PRAYER_CALCULATION_METHODS, 
  ASR_JURISTIC_OPTIONS, 
  DEFAULT_APP_SETTINGS, 
  FONT_SIZE_OPTIONS, 
  AVAILABLE_TRANSLATIONS,
  LANGUAGE_OPTIONS,
  THEME_OPTIONS
} from '../constants';
import { CogIcon, CheckCircleIcon, ChevronDownIcon, GlobeAltIcon, SunIcon, MoonIcon } from './icons'; // Added GlobeAltIcon, SunIcon, MoonIcon

interface SettingsSectionProps {
  currentSettings: AppSettings;
  onSettingsChange: (newSettings: AppSettings) => void;
}

const API_KEY_STORAGE_KEY = 'geminiApiKey';

export const SettingsSection: React.FC<SettingsSectionProps> = ({ currentSettings, onSettingsChange }) => {
  const [settings, setSettings] = useState<AppSettings>(currentSettings);
  const [apiKeyInput, setApiKeyInput] = useState<string>('');
  const [savedMessage, setSavedMessage] = useState<string>('');

  useEffect(() => {
    setSettings(currentSettings);
    const storedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
    if (storedApiKey) {
      setApiKeyInput(storedApiKey);
    }
  }, [currentSettings]);

  const handleGeneralSettingChange = <K extends keyof GeneralSettings>(
    key: K, 
    value: GeneralSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      general: {
        ...prev.general,
        [key]: value,
      },
    }));
  };

  const handlePrayerSettingChange = <K extends keyof PrayerTimesSettings>(
    key: K, 
    value: PrayerTimesSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      prayerTimes: {
        ...prev.prayerTimes,
        [key]: value,
      },
    }));
  };
  
  const handleQuranSettingChange = <K extends keyof QuranSettings>(
    key: K,
    value: QuranSettings[K]
  ) => {
    setSettings(prev => ({
      ...prev,
      quran: {
        ...prev.quran,
        [key]: value,
      },
    }));
  };

  const handleSaveSettings = () => {
    onSettingsChange(settings);
    if (apiKeyInput.trim()) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKeyInput.trim());
      setSavedMessage('تم حفظ الإعدادات ومفتاح API بنجاح!');
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY); 
      setSavedMessage('تم حفظ الإعدادات. لم يتم توفير مفتاح API.');
    }
    setTimeout(() => setSavedMessage(''), 4000);
  };

  const handleResetSettings = () => {
    setSettings(DEFAULT_APP_SETTINGS);
    onSettingsChange(DEFAULT_APP_SETTINGS); 
    
    localStorage.removeItem(API_KEY_STORAGE_KEY);
    setApiKeyInput('');
    
    setSavedMessage('تمت إعادة تعيين الإعدادات إلى الوضع الافتراضي ومسح مفتاح API.');
    setTimeout(() => setSavedMessage(''), 4000);
  };

  const renderSelect = <T extends string | number>(
    label: string,
    id: string,
    value: T,
    onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void,
    options: { id: T; name: string }[],
    icon?: React.ReactNode
  ) => (
    <div className="mb-4">
      <label htmlFor={id} className="block text-sm font-medium text-text-dark mb-1 text-start">{label}</label>
      <div className="relative flex items-center">
        {icon && <span className="absolute start-3 z-10 text-text-dark">{icon}</span>}
        <select
          id={id}
          value={value}
          onChange={onChange}
          className={`appearance-none w-full p-3 border border-border-default rounded-md bg-background-light text-text-default focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light ${icon ? 'ps-10' : 'ps-3'} pe-8 text-start`}
        >
          {options.map(option => (
            <option key={option.id.toString()} value={option.id}>{option.name}</option>
          ))}
        </select>
        <div className={`pointer-events-none absolute inset-y-0 ${settings.general.appLanguage === 'ar' ? 'start-0 ps-3' : 'end-0 pe-3'} flex items-center text-text-dark`}>
            <ChevronDownIcon className="w-5 h-5"/>
        </div>
      </div>
    </div>
  );
  
  const renderNumberInput = (
    label: string,
    id: string,
    value: number,
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void,
    min?: number,
    max?: number
  ) => (
    <div className="mb-4">
      <label htmlFor={id} className="block text-sm font-medium text-text-dark mb-1 text-start">{label}</label>
      <input
        type="number"
        id={id}
        value={value}
        onChange={onChange}
        min={min}
        max={max}
        className="w-full p-3 border border-border-default rounded-md bg-background-light text-text-default focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light"
      />
    </div>
  );


  return (
    <div className="space-y-8">
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg text-center">
        <h2 className="text-3xl font-bold text-primary mb-2 flex items-center justify-center">
          <CogIcon className="w-8 h-8 ms-3" /> إعدادات التطبيق
        </h2>
        <p className="text-text-dark">قم بتخصيص تجربة التطبيق الخاصة بك.</p>
      </div>
      
      {/* General Settings */}
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg">
        <h3 className="text-xl font-semibold text-primary mb-4 text-start">الإعدادات العامة</h3>
        {renderSelect(
            "لغة التطبيق",
            "appLanguage",
            settings.general.appLanguage,
            (e) => handleGeneralSettingChange('appLanguage', e.target.value as 'ar' | 'en'),
            LANGUAGE_OPTIONS,
            <GlobeAltIcon className="w-5 h-5" />
        )}
        {renderSelect(
            "مظهر التطبيق",
            "themeMode",
            settings.general.themeMode,
            (e) => handleGeneralSettingChange('themeMode', e.target.value as 'light' | 'dark'),
            THEME_OPTIONS,
            settings.general.themeMode === 'light' ? <SunIcon className="w-5 h-5" /> : <MoonIcon className="w-5 h-5" />
        )}
      </div>

      {/* API Key Settings */}
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg">
        <h3 className="text-xl font-semibold text-primary mb-4 text-start">إعدادات مفتاح Gemini API</h3>
        <div className="mb-4">
            <label htmlFor="geminiApiKey" className="block text-sm font-medium text-text-dark mb-1 text-start">
                مفتاح Gemini API (مطلوب لمولد الأدعية والأذكار)
            </label>
            <input
                type="password" 
                id="geminiApiKey"
                value={apiKeyInput}
                onChange={(e) => setApiKeyInput(e.target.value)}
                placeholder="أدخل مفتاح Gemini API الخاص بك هنا"
                className="w-full p-3 border border-border-default rounded-md bg-background-light text-text-default focus:ring-2 focus:ring-primary-light focus:ring-opacity-60 focus:border-primary-light"
            />
            <p className="text-xs text-text-dark mt-1 text-start">
                لن يتم مشاركة هذا المفتاح ويُحفظ فقط في التخزين المحلي لمتصفحك.
            </p>
        </div>
      </div>


      {/* Prayer Times Settings */}
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg">
        <h3 className="text-xl font-semibold text-primary mb-4 text-start">إعدادات مواقيت الصلاة</h3>
        {renderSelect(
            "طريقة الحساب",
            "prayerMethod",
            settings.prayerTimes.calculationMethodId,
            (e) => handlePrayerSettingChange('calculationMethodId', parseInt(e.target.value, 10)),
            PRAYER_CALCULATION_METHODS
        )}
        {renderSelect(
            "المذهب الفقهي للعصر",
            "asrMethod",
            settings.prayerTimes.asrJuristicMethod,
            (e) => handlePrayerSettingChange('asrJuristicMethod', e.target.value as 'standard' | 'hanafi'),
            ASR_JURISTIC_OPTIONS
        )}
        <h4 className="text-md font-medium text-text-dark mt-6 mb-2 text-start">تعديلات دقيقة للأوقات (دقائق):</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {renderNumberInput("الفجر", "fajrOffset", settings.prayerTimes.fajrOffset, (e) => handlePrayerSettingChange('fajrOffset', parseInt(e.target.value) || 0), -60, 60)}
            {renderNumberInput("الظهر", "dhuhrOffset", settings.prayerTimes.dhuhrOffset, (e) => handlePrayerSettingChange('dhuhrOffset', parseInt(e.target.value) || 0), -60, 60)}
            {renderNumberInput("العصر", "asrOffset", settings.prayerTimes.asrOffset, (e) => handlePrayerSettingChange('asrOffset', parseInt(e.target.value) || 0), -60, 60)}
            {renderNumberInput("المغرب", "maghribOffset", settings.prayerTimes.maghribOffset, (e) => handlePrayerSettingChange('maghribOffset', parseInt(e.target.value) || 0), -60, 60)}
            {renderNumberInput("العشاء", "ishaOffset", settings.prayerTimes.ishaOffset, (e) => handlePrayerSettingChange('ishaOffset', parseInt(e.target.value) || 0), -60, 60)}
        </div>
      </div>
      
      {/* Quran Settings */}
      <div className="p-6 bg-card-bg rounded-2xl shadow-lg">
        <h3 className="text-xl font-semibold text-primary mb-4 text-start">إعدادات القرآن</h3>
         {renderSelect(
            "الترجمة الافتراضية",
            "defaultTranslation",
            settings.quran.defaultTranslation,
            (e) => handleQuranSettingChange('defaultTranslation', e.target.value),
            AVAILABLE_TRANSLATIONS.map(trans => ({ id: trans.identifier, name: trans.name }))
        )}
        {renderSelect(
            "حجم الخط العربي",
            "arabicFontSize",
            settings.quran.arabicFontSize,
            (e) => handleQuranSettingChange('arabicFontSize', e.target.value as 'small' | 'medium' | 'large'),
            FONT_SIZE_OPTIONS
        )}
        {renderSelect(
            "حجم خط الترجمة",
            "translationFontSize",
            settings.quran.translationFontSize,
            (e) => handleQuranSettingChange('translationFontSize', e.target.value as 'small' | 'medium' | 'large'),
            FONT_SIZE_OPTIONS
        )}
      </div>

      <div className="p-6 bg-card-bg rounded-2xl shadow-lg mt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
        <button
          onClick={handleSaveSettings}
          className="w-full sm:w-auto bg-primary hover:bg-primary-dark text-white font-bold py-3 px-6 rounded-md transition duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light active:scale-95 transform transition-transform duration-75"
        >
          حفظ الإعدادات
        </button>
        <button
          onClick={handleResetSettings}
          className="w-full sm:w-auto bg-secondary-default hover:bg-secondary-dark text-text-default font-semibold py-3 px-6 rounded-md transition duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-dark active:scale-95 transform transition-transform duration-75"
        >
          إعادة التعيين إلى الافتراضي
        </button>
      </div>
       {savedMessage && (
          <div className="mt-4 p-3 bg-emerald-500 text-white rounded-lg flex items-center justify-center fixed bottom-24 end-4 z-[60] shadow-xl border border-emerald-600">
            <CheckCircleIcon className="w-5 h-5 ms-2"/> {savedMessage}
          </div>
        )}
    </div>
  );
};